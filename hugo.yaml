baseURL: https://example.org/
title: wenhaofree-文浩Free
theme: ["LoveIt"]

# determines default content language ["en", "zh-cn", "fr", "pl", ...]
# 设置默认的语言 ["en", "zh-cn", "fr", "pl", ...]
defaultContentLanguage: "zh-cn"
# language code ["en", "zh-CN", "fr", "pl", ...]
# 网站语言, 仅在这里 CN 大写 ["en", "zh-CN", "fr", "pl", ...]
languageCode: "zh-CN"
# language name ["English", "简体中文", "Français", "Polski", ...]
# 语言名称 ["English", "简体中文", "Français", "Polski", ...]
languageName: "简体中文"
# whether to include Chinese/Japanese/Korean
# 是否包括中日韩文字
hasCJKLanguage: true

# whether to use robots.txt
# 是否使用 robots.txt
enableRobotsTXT: true
# whether to use git commit log
# 是否使用 git 信息
enableGitInfo: true
# whether to use emoji code
# 是否使用 emoji 代码
enableEmoji: true

# Multilingual
# 多语言
languages:
  zh-cn:
    weight: 1
    languageCode: "zh-CN"
    languageName: "简体中文"
    hasCJKLanguage: true
    title: "wenhaofree-文浩Free"
    menu:
      main:
        - weight: 1
          identifier: "posts"
          name: "所有文章"
          url: "/posts/"
          title: ""
        - weight: 2
          identifier: "about"
          name: "关于"
          url: "/about/"
          title: ""
  en:
    weight: 2
    languageCode: "en"
    languageName: "English"
    hasCJKLanguage: false
    title: "wenhaofree-文浩Free"
    menu:
      main:
        - weight: 1
          identifier: "posts"
          name: "All Articles"
          url: "/posts/"
          title: ""
        - weight: 2
          identifier: "about"
          name: "About"
          url: "/about/"
          title: ""
