#!/bin/bash

echo "Testing Hugo Blog Configuration..."
echo "================================="

# Test if Hugo server is running
echo "1. Testing if site is accessible..."
if curl -s http://localhost:1313/ > /dev/null; then
    echo "✅ Site is accessible at http://localhost:1313/"
else
    echo "❌ Site is not accessible. Make sure <PERSON> server is running."
    exit 1
fi

# Test Chinese homepage
echo "2. Testing Chinese homepage..."
if curl -s http://localhost:1313/ | grep -q "所有文章"; then
    echo "✅ Chinese navigation menu found"
else
    echo "❌ Chinese navigation menu not found"
fi

# Test English homepage
echo "3. Testing English homepage..."
if curl -s http://localhost:1313/en/ | grep -q "All Articles"; then
    echo "✅ English navigation menu found"
else
    echo "❌ English navigation menu not found"
fi

# Test Chinese about page
echo "4. Testing Chinese about page..."
if curl -s http://localhost:1313/about/ | grep -q "关于我"; then
    echo "✅ Chinese about page working"
else
    echo "❌ Chinese about page not working"
fi

# Test English about page
echo "5. Testing English about page..."
if curl -s http://localhost:1313/en/about/ | grep -q "About Me"; then
    echo "✅ English about page working"
else
    echo "❌ English about page not working"
fi

# Test Chinese posts page
echo "6. Testing Chinese posts page..."
if curl -s http://localhost:1313/posts/ | grep -q "所有文章"; then
    echo "✅ Chinese posts page working"
else
    echo "❌ Chinese posts page not working"
fi

# Test English posts page
echo "7. Testing English posts page..."
if curl -s http://localhost:1313/en/posts/ | grep -q "All Articles"; then
    echo "✅ English posts page working"
else
    echo "❌ English posts page not working"
fi

# Test language switching functionality
echo "8. Testing language switching..."
if curl -s http://localhost:1313/ | grep -q "language-select"; then
    echo "✅ Language selector found"
else
    echo "❌ Language selector not found"
fi

echo ""
echo "Testing completed!"
echo "You can manually verify the following:"
echo "- Visit http://localhost:1313/ for Chinese version"
echo "- Visit http://localhost:1313/en/ for English version"
echo "- Check navigation menu items: '所有文章' and '关于' (Chinese)"
echo "- Check navigation menu items: 'All Articles' and 'About' (English)"
echo "- Test language switching using the globe icon in the header"
