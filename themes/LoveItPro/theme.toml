# theme.toml template for a Hugo theme
# See https://github.com/gohugoio/hugoThemes#themetoml for an example

name = "LoveItPro"
license = "MIT"
licenselink = "https://github.com/wenhaofree/LoveItPro/blob/master/LICENSE"
description = "A clean, elegant but advanced Hugo theme with modern features and responsive design"
homepage = "https://github.com/wenhaofree/LoveItPro"
tags = ["blog", "responsive", "multilingual", "modern", "clean", "elegant", "bootstrap", "seo"]
features = ["responsive", "multilingual", "search", "carousel", "cards", "sidebar", "seo", "syntax-highlighting", "comments"]
min_version = "0.128.0"

[author]
  name = "<PERSON>H<PERSON>"
  homepage = "https://github.com/wenhaofree"

# If porting an existing theme
[original]
  name = "LoveIt"
  homepage = "https://github.com/dillonzq/LoveIt"
  repo = "https://github.com/dillonzq/LoveIt"
