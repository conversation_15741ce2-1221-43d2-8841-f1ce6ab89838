// LoveItPro Theme Search Functionality
// ====================================

(function() {
    'use strict';
    
    let searchData = [];
    let fuse = null;
    const config = window.themeConfig?.search || {};
    
    // Initialize search when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        if (config.enable) {
            initSearch();
        }
    });
    
    // Initialize Search
    function initSearch() {
        loadSearchData();
        bindSearchEvents();
    }
    
    // Load Search Data
    function loadSearchData() {
        const searchDataElement = document.getElementById('search-data');
        if (searchDataElement) {
            try {
                searchData = JSON.parse(searchDataElement.textContent);
                initFuse();
            } catch (error) {
                console.error('Error parsing search data:', error);
            }
        }
    }
    
    // Initialize Fuse.js
    function initFuse() {
        if (typeof Fuse !== 'undefined' && searchData.length > 0) {
            const options = {
                keys: [
                    { name: 'title', weight: 0.8 },
                    { name: 'content', weight: 0.5 },
                    { name: 'summary', weight: 0.6 },
                    { name: 'categories', weight: 0.3 },
                    { name: 'tags', weight: 0.3 }
                ],
                threshold: 0.3,
                includeScore: true,
                includeMatches: true,
                minMatchCharLength: 2
            };
            
            fuse = new Fuse(searchData, options);
        }
    }
    
    // Bind Search Events
    function bindSearchEvents() {
        // Modal search input
        const searchInput = document.getElementById('search-input');
        const clearSearchBtn = document.getElementById('clear-search');
        const searchCategory = document.getElementById('search-category');
        const searchTag = document.getElementById('search-tag');
        const searchSort = document.getElementById('search-sort');
        
        // Hero search input
        const heroSearchInput = document.getElementById('hero-search');
        const heroSearchBtn = document.getElementById('hero-search-btn');
        
        // Search input events
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearch();
                }
            });
        }
        
        // Clear search button
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                clearSearchResults();
                showInitialState();
            });
        }
        
        // Filter events
        if (searchCategory) {
            searchCategory.addEventListener('change', handleSearch);
        }
        
        if (searchTag) {
            searchTag.addEventListener('change', handleSearch);
        }
        
        if (searchSort) {
            searchSort.addEventListener('change', handleSearch);
        }
        
        // Hero search events
        if (heroSearchInput) {
            heroSearchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    performHeroSearch();
                }
            });
        }
        
        if (heroSearchBtn) {
            heroSearchBtn.addEventListener('click', performHeroSearch);
        }
        
        // Modal events
        const searchModal = document.getElementById('searchModal');
        if (searchModal) {
            searchModal.addEventListener('shown.bs.modal', function() {
                searchInput.focus();
            });
            
            searchModal.addEventListener('hidden.bs.modal', function() {
                clearSearchResults();
                showInitialState();
            });
        }
    }
    
    // Handle Search
    function handleSearch() {
        const searchInput = document.getElementById('search-input');
        const query = searchInput ? searchInput.value.trim() : '';
        
        if (query.length === 0) {
            clearSearchResults();
            showInitialState();
            return;
        }
        
        if (query.length < 2) {
            return;
        }
        
        showLoadingState();
        
        // Perform search with a small delay to show loading state
        setTimeout(() => {
            const results = performSearch(query);
            displaySearchResults(results, query);
        }, 100);
    }
    
    // Perform Search
    function performSearch(query) {
        if (!fuse || !query) {
            return [];
        }
        
        let results = fuse.search(query);
        
        // Apply filters
        const categoryFilter = document.getElementById('search-category')?.value;
        const tagFilter = document.getElementById('search-tag')?.value;
        
        if (categoryFilter) {
            results = results.filter(result => 
                result.item.categories && result.item.categories.includes(categoryFilter)
            );
        }
        
        if (tagFilter) {
            results = results.filter(result => 
                result.item.tags && result.item.tags.includes(tagFilter)
            );
        }
        
        // Apply sorting
        const sortBy = document.getElementById('search-sort')?.value || 'relevance';
        
        if (sortBy === 'date') {
            results.sort((a, b) => new Date(b.item.date) - new Date(a.item.date));
        } else if (sortBy === 'title') {
            results.sort((a, b) => a.item.title.localeCompare(b.item.title));
        }
        // 'relevance' is already sorted by Fuse.js
        
        // Limit results
        const maxResults = config.maxResults || 10;
        return results.slice(0, maxResults);
    }
    
    // Display Search Results
    function displaySearchResults(results, query) {
        const resultsContainer = document.getElementById('search-results-list');
        const searchStats = document.getElementById('search-stats');
        
        hideAllStates();
        
        if (results.length === 0) {
            showNoResultsState();
            return;
        }
        
        // Update stats
        if (searchStats) {
            searchStats.textContent = `Found ${results.length} result${results.length !== 1 ? 's' : ''} for "${query}"`;
        }
        
        // Generate results HTML
        const resultsHTML = results.map(result => {
            const item = result.item;
            const excerpt = highlightMatches(item.summary || item.content.substring(0, 150), result.matches, 'summary') + '...';
            const title = highlightMatches(item.title, result.matches, 'title');
            
            return `
                <div class="search-result-item">
                    <div class="result-title">
                        <a href="${item.url}">${title}</a>
                    </div>
                    <div class="result-excerpt">${excerpt}</div>
                    <div class="result-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            ${formatDate(item.date)}
                        </span>
                        ${item.categories && item.categories.length > 0 ? `
                            <span class="meta-item">
                                <i class="fas fa-folder"></i>
                                ${item.categories[0]}
                            </span>
                        ` : ''}
                        ${item.author ? `
                            <span class="meta-item">
                                <i class="fas fa-user"></i>
                                ${item.author}
                            </span>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');
        
        if (resultsContainer) {
            resultsContainer.innerHTML = resultsHTML;
            resultsContainer.style.display = 'block';
        }
    }
    
    // Highlight Search Matches
    function highlightMatches(text, matches, key) {
        if (!matches || !text) return text;
        
        const relevantMatches = matches.filter(match => match.key === key);
        if (relevantMatches.length === 0) return text;
        
        let highlightedText = text;
        const highlights = [];
        
        relevantMatches.forEach(match => {
            match.indices.forEach(([start, end]) => {
                highlights.push({ start, end });
            });
        });
        
        // Sort highlights by start position (descending) to avoid index shifting
        highlights.sort((a, b) => b.start - a.start);
        
        highlights.forEach(({ start, end }) => {
            const before = highlightedText.substring(0, start);
            const highlighted = highlightedText.substring(start, end + 1);
            const after = highlightedText.substring(end + 1);
            highlightedText = before + `<mark>${highlighted}</mark>` + after;
        });
        
        return highlightedText;
    }
    
    // Hero Search
    function performHeroSearch() {
        const heroSearchInput = document.getElementById('hero-search');
        const query = heroSearchInput ? heroSearchInput.value.trim() : '';
        
        if (query) {
            // Open search modal and perform search
            const searchModal = document.getElementById('searchModal');
            const searchInput = document.getElementById('search-input');
            
            if (searchModal && searchInput) {
                searchInput.value = query;
                const modal = new bootstrap.Modal(searchModal);
                modal.show();
                
                // Perform search after modal is shown
                setTimeout(() => {
                    handleSearch();
                }, 100);
            }
        }
    }
    
    // State Management
    function showInitialState() {
        hideAllStates();
        const initialState = document.querySelector('.search-initial');
        if (initialState) {
            initialState.style.display = 'block';
        }
    }
    
    function showLoadingState() {
        hideAllStates();
        const loadingState = document.querySelector('.search-loading');
        if (loadingState) {
            loadingState.style.display = 'block';
        }
    }
    
    function showNoResultsState() {
        hideAllStates();
        const noResultsState = document.querySelector('.search-no-results');
        if (noResultsState) {
            noResultsState.style.display = 'block';
        }
    }
    
    function hideAllStates() {
        const states = ['.search-initial', '.search-loading', '.search-no-results'];
        states.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                element.style.display = 'none';
            }
        });
        
        const resultsContainer = document.getElementById('search-results-list');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }
    
    function clearSearchResults() {
        const resultsContainer = document.getElementById('search-results-list');
        const searchStats = document.getElementById('search-stats');
        
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
            resultsContainer.style.display = 'none';
        }
        
        if (searchStats) {
            searchStats.textContent = '';
        }
    }
    
    // Utility Functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
})();
