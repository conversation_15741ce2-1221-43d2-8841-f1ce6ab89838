// LoveItPro Theme Main JavaScript
// ===============================

(function() {
    'use strict';
    
    // Theme Configuration
    const config = window.themeConfig || {};
    
    // DOM Ready
    document.addEventListener('DOMContentLoaded', function() {
        initTheme();
        initBackToTop();
        initViewToggle();
        initSmoothScroll();
        initLazyLoading();
        initTooltips();
        initShareButtons();
        initBookmarks();
        initNewsletter();
        initCarousel();
    });
    
    // Theme Management
    function initTheme() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const storageKey = config.theme?.storageKey || 'theme-preference';
        
        // Get saved theme or default
        const savedTheme = localStorage.getItem(storageKey) || config.theme?.default || 'auto';
        
        // Apply theme
        function applyTheme(theme) {
            if (theme === 'auto') {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
                themeIcon.className = 'fas fa-adjust';
            } else {
                document.documentElement.setAttribute('data-theme', theme);
                themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
            localStorage.setItem(storageKey, theme);
        }
        
        // Initialize theme
        applyTheme(savedTheme);
        
        // Theme toggle handler
        if (themeToggle) {
            themeToggle.addEventListener('click', function() {
                const currentTheme = localStorage.getItem(storageKey) || 'auto';
                const themes = ['auto', 'light', 'dark'];
                const currentIndex = themes.indexOf(currentTheme);
                const nextTheme = themes[(currentIndex + 1) % themes.length];
                applyTheme(nextTheme);
            });
        }
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function() {
            if (localStorage.getItem(storageKey) === 'auto') {
                applyTheme('auto');
            }
        });
    }
    
    // Back to Top Button
    function initBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        
        if (backToTopBtn) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('show');
                } else {
                    backToTopBtn.classList.remove('show');
                }
            });
            
            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }
    
    // View Toggle (Grid/List)
    function initViewToggle() {
        const viewToggleBtns = document.querySelectorAll('[data-view]');
        const postsContainer = document.getElementById('posts-container');
        
        viewToggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');
                
                // Update active button
                viewToggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Update container class
                if (postsContainer) {
                    postsContainer.className = view === 'list' ? 'posts-list' : 'posts-grid';
                }
                
                // Save preference
                localStorage.setItem('view-preference', view);
            });
        });
        
        // Restore saved view preference
        const savedView = localStorage.getItem('view-preference');
        if (savedView && postsContainer) {
            const targetBtn = document.querySelector(`[data-view="${savedView}"]`);
            if (targetBtn) {
                targetBtn.click();
            }
        }
    }
    
    // Smooth Scroll
    function initSmoothScroll() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    // Lazy Loading for Images
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    // Initialize Tooltips
    function initTooltips() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }
    
    // Share Buttons
    function initShareButtons() {
        // Twitter share
        window.shareToTwitter = function(url, title) {
            const shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            window.open(shareUrl, '_blank', 'width=600,height=400');
        };
        
        // Facebook share
        window.shareToFacebook = function(url) {
            const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(shareUrl, '_blank', 'width=600,height=400');
        };
        
        // Copy to clipboard
        window.copyToClipboard = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast(config.i18n?.copied || 'Copied!', 'success');
                }).catch(() => {
                    showToast(config.i18n?.copyFailed || 'Copy failed', 'error');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showToast(config.i18n?.copied || 'Copied!', 'success');
                } catch (err) {
                    showToast(config.i18n?.copyFailed || 'Copy failed', 'error');
                }
                document.body.removeChild(textArea);
            }
        };
    }
    
    // Bookmark Functionality
    function initBookmarks() {
        const bookmarkBtns = document.querySelectorAll('.bookmark-btn');
        const bookmarks = JSON.parse(localStorage.getItem('bookmarks') || '[]');
        
        // Update bookmark button states
        bookmarkBtns.forEach(btn => {
            const url = btn.getAttribute('data-url');
            if (bookmarks.some(bookmark => bookmark.url === url)) {
                btn.classList.add('bookmarked');
                btn.querySelector('i').className = 'fas fa-bookmark';
            }
        });
        
        // Bookmark button click handler
        bookmarkBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const url = this.getAttribute('data-url');
                const title = this.getAttribute('data-title');
                const icon = this.querySelector('i');
                
                const bookmarkIndex = bookmarks.findIndex(bookmark => bookmark.url === url);
                
                if (bookmarkIndex > -1) {
                    // Remove bookmark
                    bookmarks.splice(bookmarkIndex, 1);
                    this.classList.remove('bookmarked');
                    icon.className = 'far fa-bookmark';
                    showToast('Bookmark removed', 'info');
                } else {
                    // Add bookmark
                    bookmarks.push({ url, title, date: new Date().toISOString() });
                    this.classList.add('bookmarked');
                    icon.className = 'fas fa-bookmark';
                    showToast('Bookmark added', 'success');
                }
                
                localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
            });
        });
    }
    
    // Newsletter Form
    function initNewsletter() {
        const newsletterForm = document.getElementById('newsletter-form');
        
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const submitBtn = document.getElementById('newsletter-submit');
                const submitText = submitBtn.querySelector('.submit-text');
                const submitLoading = submitBtn.querySelector('.submit-loading');
                const successMsg = document.getElementById('newsletter-success');
                const errorMsg = document.getElementById('newsletter-error');
                
                // Show loading state
                submitText.style.display = 'none';
                submitLoading.style.display = 'inline';
                submitBtn.disabled = true;
                
                // Hide previous messages
                successMsg.style.display = 'none';
                errorMsg.style.display = 'none';
                
                // Simulate form submission (replace with actual implementation)
                setTimeout(() => {
                    // Reset button state
                    submitText.style.display = 'inline';
                    submitLoading.style.display = 'none';
                    submitBtn.disabled = false;
                    
                    // Show success message
                    successMsg.style.display = 'block';
                    newsletterForm.reset();
                }, 2000);
            });
        }
    }
    
    // Carousel Enhancement
    function initCarousel() {
        const carousel = document.getElementById('heroCarousel');
        
        if (carousel && config.carousel?.enable) {
            // Pause on hover
            carousel.addEventListener('mouseenter', function() {
                if (typeof bootstrap !== 'undefined') {
                    const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                    if (carouselInstance) {
                        carouselInstance.pause();
                    }
                }
            });
            
            carousel.addEventListener('mouseleave', function() {
                if (typeof bootstrap !== 'undefined' && config.carousel?.autoplay) {
                    const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                    if (carouselInstance) {
                        carouselInstance.cycle();
                    }
                }
            });
        }
    }
    
    // Toast Notification
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add to page
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Show toast
        if (typeof bootstrap !== 'undefined') {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove from DOM after hiding
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }
    }
    
})();
