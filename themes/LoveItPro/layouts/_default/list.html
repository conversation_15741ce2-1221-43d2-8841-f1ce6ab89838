{{- define "main" -}}
<div class="list-page">
    <div class="container">
        <div class="row">
            {{- /* Main Content */ -}}
            <div class="col-lg-{{ if .Site.Params.sidebar.enable }}8{{ else }}12{{ end }}">
                {{- /* Page Header */ -}}
                <header class="page-header">
                    {{- /* Breadcrumb */ -}}
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ .Site.Home.RelPermalink }}">
                                    <i class="fas fa-home"></i> {{ i18n "home" | default "Home" }}
                                </a>
                            </li>
                            {{- if eq .Kind "section" -}}
                                <li class="breadcrumb-item active" aria-current="page">{{ .Title }}</li>
                            {{- else if eq .Kind "taxonomy" -}}
                                <li class="breadcrumb-item">
                                    <a href="{{ printf "/%s/" .Data.Plural | relLangURL }}">
                                        {{ .Data.Plural | title }}
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">{{ .Title }}</li>
                            {{- else -}}
                                <li class="breadcrumb-item active" aria-current="page">{{ .Title }}</li>
                            {{- end -}}
                        </ol>
                    </nav>
                    
                    {{- /* Page Title */ -}}
                    <h1 class="page-title">
                        {{- if eq .Kind "taxonomy" -}}
                            {{ .Data.Singular | title }}: {{ .Title }}
                        {{- else -}}
                            {{ .Title }}
                        {{- end -}}
                    </h1>
                    
                    {{- /* Page Description */ -}}
                    {{- with .Description -}}
                        <p class="page-description">{{ . }}</p>
                    {{- end -}}
                    
                    {{- /* Post Count */ -}}
                    {{- if .Pages -}}
                        <div class="post-count">
                            <i class="fas fa-file-alt"></i>
                            {{ len .Pages }} {{ i18n "posts" | default "posts" }}
                        </div>
                    {{- end -}}
                </header>
                
                {{- /* Content */ -}}
                {{- with .Content -}}
                    <div class="page-content">
                        {{ . }}
                    </div>
                {{- end -}}
                
                {{- /* Posts List */ -}}
                {{- if .Pages -}}
                    <section class="posts-list">
                        {{- /* View Toggle */ -}}
                        <div class="list-controls mb-4">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="view-toggle">
                                        <div class="btn-group" role="group" aria-label="{{ i18n "viewToggle" | default "View toggle" }}">
                                            <button type="button" class="btn btn-outline-primary active" data-view="grid">
                                                <i class="fas fa-th"></i> {{ i18n "gridView" | default "Grid" }}
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" data-view="list">
                                                <i class="fas fa-list"></i> {{ i18n "listView" | default "List" }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="sort-options">
                                        <select class="form-select" id="sort-posts">
                                            <option value="date-desc">{{ i18n "sortByDateDesc" | default "Newest First" }}</option>
                                            <option value="date-asc">{{ i18n "sortByDateAsc" | default "Oldest First" }}</option>
                                            <option value="title-asc">{{ i18n "sortByTitleAsc" | default "Title A-Z" }}</option>
                                            <option value="title-desc">{{ i18n "sortByTitleDesc" | default "Title Z-A" }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {{- /* Posts Container */ -}}
                        <div id="posts-container" class="posts-grid">
                            <div class="row">
                                {{- $paginator := .Paginate .Pages -}}
                                {{- range $paginator.Pages -}}
                                    <div class="col-md-6 col-lg-4 mb-4 post-item" 
                                         data-date="{{ .Date.Unix }}" 
                                         data-title="{{ .Title | lower }}">
                                        {{- partial "components/article-card.html" (dict "page" .) -}}
                                    </div>
                                {{- end -}}
                            </div>
                        </div>
                        
                        {{- /* Pagination */ -}}
                        {{- if gt $paginator.TotalPages 1 -}}
                            <nav aria-label="{{ i18n "pagination" | default "Pagination" }}" class="mt-5">
                                {{- partial "components/pagination.html" $paginator -}}
                            </nav>
                        {{- end -}}
                    </section>
                {{- else -}}
                    {{- /* No Posts Message */ -}}
                    <div class="no-posts text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h3>{{ i18n "noPosts" | default "No posts found" }}</h3>
                        <p class="text-muted">{{ i18n "noPostsDescription" | default "There are no posts to display." }}</p>
                        <a href="{{ .Site.Home.RelPermalink }}" class="btn btn-primary">
                            {{ i18n "backToHome" | default "Back to Home" }}
                        </a>
                    </div>
                {{- end -}}
            </div>
            
            {{- /* Sidebar */ -}}
            {{- if .Site.Params.sidebar.enable -}}
                <div class="col-lg-4">
                    {{- partial "sidebar/sidebar.html" . -}}
                </div>
            {{- end -}}
        </div>
    </div>
</div>

{{- /* JSON-LD Structured Data */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "{{ .Title }}",
    "description": "{{ with .Description }}{{ . }}{{ else }}{{ .Site.Params.description }}{{ end }}",
    "url": "{{ .Permalink }}",
    "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": {{ len .Pages }},
        "itemListElement": [
            {{- range $index, $page := .Pages -}}
                {{- if gt $index 0 }},{{ end }}
                {
                    "@type": "BlogPosting",
                    "position": {{ add $index 1 }},
                    "name": "{{ $page.Title }}",
                    "url": "{{ $page.Permalink }}",
                    "datePublished": "{{ $page.Date.Format "2006-01-02T15:04:05Z07:00" }}"
                }
            {{- end -}}
        ]
    }
}
</script>
{{- end -}}
