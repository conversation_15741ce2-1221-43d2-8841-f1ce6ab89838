{{- define "main" -}}
<div class="single-post">
    <div class="container">
        <div class="row">
            {{- /* Main Content */ -}}
            <div class="col-lg-{{ if .Site.Params.sidebar.enable }}8{{ else }}12{{ end }}">
                <article class="post-content">
                    {{- /* Post Header */ -}}
                    <header class="post-header">
                        {{- /* Breadcrumb */ -}}
                        <nav aria-label="breadcrumb" class="mb-3">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="{{ .Site.Home.RelPermalink }}">
                                        <i class="fas fa-home"></i> {{ i18n "home" | default "Home" }}
                                    </a>
                                </li>
                                {{- with .Section -}}
                                    <li class="breadcrumb-item">
                                        <a href="{{ printf "/%s/" . | relLangURL }}">
                                            {{ . | title }}
                                        </a>
                                    </li>
                                {{- end -}}
                                <li class="breadcrumb-item active" aria-current="page">{{ .Title }}</li>
                            </ol>
                        </nav>
                        
                        {{- /* Post Title */ -}}
                        <h1 class="post-title">{{ .Title }}</h1>
                        
                        {{- /* Post Meta */ -}}
                        <div class="post-meta">
                            <div class="meta-item">
                                <i class="fas fa-calendar-alt"></i>
                                <time datetime="{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}">
                                    {{ .Date.Format (.Site.Params.dateFormat | default "January 2, 2006") }}
                                </time>
                            </div>
                            
                            {{- with .Params.author | default .Site.Params.author -}}
                                <div class="meta-item">
                                    <i class="fas fa-user"></i>
                                    <span>{{ . }}</span>
                                </div>
                            {{- end -}}
                            
                            {{- if .Site.Params.article.readingTime.enable -}}
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ partial "components/reading-time.html" . }}</span>
                                </div>
                            {{- end -}}
                            
                            {{- with .Params.categories -}}
                                <div class="meta-item">
                                    <i class="fas fa-folder"></i>
                                    {{- range $index, $category := . -}}
                                        {{- if gt $index 0 }}, {{ end -}}
                                        <a href="{{ printf "/categories/%s/" ($category | urlize) | relLangURL }}">{{ $category }}</a>
                                    {{- end -}}
                                </div>
                            {{- end -}}
                            
                            <div class="meta-item">
                                <i class="fas fa-eye"></i>
                                <span id="post-views">{{ i18n "loading" | default "Loading..." }}</span>
                            </div>
                        </div>
                        
                        {{- /* Featured Image */ -}}
                        {{- with .Params.image -}}
                            <div class="post-image">
                                <img src="{{ . | relURL }}" alt="{{ $.Title }}" class="img-fluid rounded">
                            </div>
                        {{- end -}}
                    </header>
                    
                    {{- /* Table of Contents */ -}}
                    {{- if and .TableOfContents (ne .TableOfContents "<nav id=\"TableOfContents\"></nav>") -}}
                        <div class="table-of-contents">
                            <h3>{{ i18n "tableOfContents" | default "Table of Contents" }}</h3>
                            {{ .TableOfContents }}
                        </div>
                    {{- end -}}
                    
                    {{- /* Post Content */ -}}
                    <div class="post-body">
                        {{ .Content }}
                    </div>
                    
                    {{- /* Post Tags */ -}}
                    {{- with .Params.tags -}}
                        <div class="post-tags">
                            <h4>{{ i18n "tags" | default "Tags" }}</h4>
                            <div class="tags-list">
                                {{- range . -}}
                                    <a href="{{ printf "/tags/%s/" (. | urlize) | relLangURL }}" class="tag-link">
                                        <span class="badge bg-secondary">#{{ . }}</span>
                                    </a>
                                {{- end -}}
                            </div>
                        </div>
                    {{- end -}}
                    
                    {{- /* Post Footer */ -}}
                    <footer class="post-footer">
                        {{- /* Share Buttons */ -}}
                        <div class="share-buttons">
                            <h4>{{ i18n "sharePost" | default "Share this post" }}</h4>
                            {{- partial "components/share-buttons.html" . -}}
                        </div>
                        
                        {{- /* Post Navigation */ -}}
                        <nav class="post-navigation">
                            <div class="row">
                                {{- with .PrevInSection -}}
                                    <div class="col-md-6">
                                        <div class="nav-previous">
                                            <span class="nav-label">{{ i18n "previousPost" | default "Previous Post" }}</span>
                                            <a href="{{ .RelPermalink }}" class="nav-link">
                                                <i class="fas fa-chevron-left"></i> {{ .Title }}
                                            </a>
                                        </div>
                                    </div>
                                {{- end -}}
                                
                                {{- with .NextInSection -}}
                                    <div class="col-md-6">
                                        <div class="nav-next">
                                            <span class="nav-label">{{ i18n "nextPost" | default "Next Post" }}</span>
                                            <a href="{{ .RelPermalink }}" class="nav-link">
                                                {{ .Title }} <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                {{- end -}}
                            </div>
                        </nav>
                    </footer>
                </article>
                
                {{- /* Related Posts */ -}}
                {{- $related := .Site.RegularPages.Related . | first 3 -}}
                {{- if $related -}}
                    <section class="related-posts">
                        <h3>{{ i18n "relatedPosts" | default "Related Posts" }}</h3>
                        <div class="row">
                            {{- range $related -}}
                                <div class="col-md-4 mb-3">
                                    {{- partial "components/article-card.html" (dict "page" . "compact" true) -}}
                                </div>
                            {{- end -}}
                        </div>
                    </section>
                {{- end -}}
                
                {{- /* Comments */ -}}
                {{- if .Site.Params.comments.enable -}}
                    <section class="comments">
                        {{- partial "components/comments.html" . -}}
                    </section>
                {{- end -}}
            </div>
            
            {{- /* Sidebar */ -}}
            {{- if .Site.Params.sidebar.enable -}}
                <div class="col-lg-4">
                    {{- partial "sidebar/sidebar.html" . -}}
                </div>
            {{- end -}}
        </div>
    </div>
</div>

{{- /* JSON-LD Structured Data */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": "{{ .Title }}",
    "description": "{{ with .Description }}{{ . }}{{ else }}{{ .Summary }}{{ end }}",
    "url": "{{ .Permalink }}",
    "datePublished": "{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}",
    "dateModified": "{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" }}",
    "author": {
        "@type": "Person",
        "name": "{{ .Params.author | default .Site.Params.author }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ .Site.Params.seo.image | absURL }}"
        }
    }{{- with .Params.image -}},
    "image": {
        "@type": "ImageObject",
        "url": "{{ . | absURL }}"
    }{{- end -}}
}
</script>
{{- end -}}
