<!DOCTYPE html>
<html lang="{{ .Site.Language.Lang }}" dir="{{ .Site.Language.LanguageDirection | default "ltr" }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    {{- /* Title */ -}}
    <title>
        {{- if .IsHome -}}
            {{ .Site.Title }}
            {{- with .Site.Params.subtitle }} - {{ . }}{{ end -}}
        {{- else -}}
            {{ .Title }} - {{ .Site.Title }}
        {{- end -}}
    </title>
    
    {{- /* Meta Description */ -}}
    {{- with .Description -}}
        <meta name="description" content="{{ . }}">
    {{- else -}}
        {{- with .Site.Params.description -}}
            <meta name="description" content="{{ . }}">
        {{- end -}}
    {{- end -}}
    
    {{- /* Keywords */ -}}
    {{- with .Params.tags -}}
        <meta name="keywords" content="{{ delimit . ", " }}">
    {{- else -}}
        {{- with .Site.Params.keywords -}}
            <meta name="keywords" content="{{ delimit . ", " }}">
        {{- end -}}
    {{- end -}}
    
    {{- /* Author */ -}}
    {{- with .Params.author | default .Site.Params.author -}}
        <meta name="author" content="{{ . }}">
    {{- end -}}
    
    {{- /* Canonical URL */ -}}
    <link rel="canonical" href="{{ .Permalink }}">
    
    {{- /* RSS */ -}}
    {{- with .OutputFormats.Get "rss" -}}
        <link rel="alternate" type="application/rss+xml" title="{{ $.Site.Title }}" href="{{ .Permalink }}">
    {{- end -}}
    
    {{- /* Favicon */ -}}
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    {{- /* SEO and Social Media Meta Tags */ -}}
    {{- partial "head/seo.html" . -}}
    
    {{- /* CSS Assets */ -}}
    {{- partial "head/css.html" . -}}
    
    {{- /* Custom Head */ -}}
    {{- partial "head/custom.html" . -}}
</head>

<body class="theme-{{ .Site.Params.defaultTheme | default "auto" }}">
    {{- /* Skip to main content for accessibility */ -}}
    <a class="skip-link sr-only" href="#main-content">{{ i18n "skipToContent" | default "Skip to main content" }}</a>
    
    {{- /* Header */ -}}
    {{- partial "header/header.html" . -}}
    
    {{- /* Main Content */ -}}
    <main id="main-content" class="main-content">
        {{- block "main" . }}{{ end -}}
    </main>
    
    {{- /* Sidebar */ -}}
    {{- if .Site.Params.sidebar.enable -}}
        {{- partial "sidebar/sidebar.html" . -}}
    {{- end -}}
    
    {{- /* Footer */ -}}
    {{- partial "footer/footer.html" . -}}
    
    {{- /* JavaScript Assets */ -}}
    {{- partial "footer/js.html" . -}}
    
    {{- /* Custom Footer */ -}}
    {{- partial "footer/custom.html" . -}}
    
    {{- /* Back to Top Button */ -}}
    <button id="back-to-top" class="back-to-top" aria-label="{{ i18n "backToTop" | default "Back to top" }}">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    {{- /* Search Modal */ -}}
    {{- if .Site.Params.header.search.enable -}}
        {{- partial "components/search-modal.html" . -}}
    {{- end -}}
</body>
</html>
