{{- define "main" -}}
<div class="contact-page">
    <div class="container">
        <div class="row">
            {{- /* Main Content */ -}}
            <div class="col-lg-8">
                <article class="contact-content">
                    {{- /* Page Header */ -}}
                    <header class="page-header">
                        <h1 class="page-title">{{ .Title }}</h1>
                        {{- with .Description -}}
                            <p class="page-description">{{ . }}</p>
                        {{- end -}}
                    </header>
                    
                    {{- /* Page Content */ -}}
                    {{- with .Content -}}
                        <div class="page-content">
                            {{ . }}
                        </div>
                    {{- end -}}
                    
                    {{- /* Contact Form */ -}}
                    <div class="contact-form-section">
                        <h3>{{ i18n "sendMessage" | default "Send a Message" }}</h3>
                        <form class="contact-form" id="contact-form" action="{{ .Site.Params.contact.action | default "#" }}" method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact-name" class="form-label">
                                            {{ i18n "name" | default "Name" }} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="contact-name" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact-email" class="form-label">
                                            {{ i18n "email" | default "Email" }} <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" class="form-control" id="contact-email" name="email" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact-subject" class="form-label">
                                    {{ i18n "subject" | default "Subject" }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="contact-subject" name="subject" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="contact-message" class="form-label">
                                    {{ i18n "message" | default "Message" }} <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" id="contact-message" name="message" rows="6" required></textarea>
                            </div>
                            
                            {{- /* Privacy Checkbox */ -}}
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="contact-privacy" name="privacy" required>
                                <label class="form-check-label" for="contact-privacy">
                                    <small>
                                        {{ i18n "privacyAgreement" | default "I agree to the" }}
                                        <a href="{{ "privacy" | relLangURL }}" target="_blank">{{ i18n "privacyPolicy" | default "Privacy Policy" }}</a>
                                    </small>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary" id="contact-submit">
                                <span class="submit-text">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    {{ i18n "sendMessage" | default "Send Message" }}
                                </span>
                                <span class="submit-loading" style="display: none;">
                                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                    {{ i18n "sending" | default "Sending..." }}
                                </span>
                            </button>
                        </form>
                        
                        {{- /* Success Message */ -}}
                        <div class="alert alert-success mt-3" id="contact-success" style="display: none;">
                            <i class="fas fa-check-circle me-1"></i>
                            {{ i18n "contactSuccess" | default "Thank you for your message! I'll get back to you soon." }}
                        </div>
                        
                        {{- /* Error Message */ -}}
                        <div class="alert alert-danger mt-3" id="contact-error" style="display: none;">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            <span id="contact-error-message">
                                {{ i18n "contactError" | default "Something went wrong. Please try again." }}
                            </span>
                        </div>
                    </div>
                </article>
            </div>
            
            {{- /* Sidebar */ -}}
            <div class="col-lg-4">
                <aside class="contact-sidebar">
                    {{- /* Contact Information */ -}}
                    <div class="contact-info">
                        <h4>{{ i18n "contactInfo" | default "Contact Information" }}</h4>
                        
                        {{- with .Site.Params.contact.email -}}
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <div class="contact-details">
                                    <strong>{{ i18n "email" | default "Email" }}</strong>
                                    <a href="mailto:{{ . }}">{{ . }}</a>
                                </div>
                            </div>
                        {{- end -}}
                        
                        {{- with .Site.Params.contact.phone -}}
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <div class="contact-details">
                                    <strong>{{ i18n "phone" | default "Phone" }}</strong>
                                    <a href="tel:{{ . }}">{{ . }}</a>
                                </div>
                            </div>
                        {{- end -}}
                        
                        {{- with .Site.Params.contact.address -}}
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div class="contact-details">
                                    <strong>{{ i18n "address" | default "Address" }}</strong>
                                    <span>{{ . }}</span>
                                </div>
                            </div>
                        {{- end -}}
                    </div>
                    
                    {{- /* Social Media */ -}}
                    {{- if .Site.Params.social -}}
                        <div class="contact-social">
                            <h4>{{ i18n "followMe" | default "Follow Me" }}</h4>
                            <div class="social-links">
                                {{- range $name, $url := .Site.Params.social -}}
                                    {{- if and $url (ne $name "Email") (ne $name "RSS") -}}
                                        <a href="{{ $url }}" class="social-link" title="{{ $name }}" target="_blank" rel="noopener">
                                            {{- if eq $name "GitHub" -}}
                                                <i class="fab fa-github"></i>
                                            {{- else if eq $name "Twitter" -}}
                                                <i class="fab fa-twitter"></i>
                                            {{- else if eq $name "LinkedIn" -}}
                                                <i class="fab fa-linkedin"></i>
                                            {{- else if eq $name "Facebook" -}}
                                                <i class="fab fa-facebook"></i>
                                            {{- else if eq $name "Instagram" -}}
                                                <i class="fab fa-instagram"></i>
                                            {{- else -}}
                                                <i class="fas fa-link"></i>
                                            {{- end -}}
                                        </a>
                                    {{- end -}}
                                {{- end -}}
                            </div>
                        </div>
                    {{- end -}}
                    
                    {{- /* Response Time */ -}}
                    <div class="response-info">
                        <h4>{{ i18n "responseTime" | default "Response Time" }}</h4>
                        <p>{{ i18n "responseTimeDescription" | default "I typically respond to messages within 24-48 hours." }}</p>
                    </div>
                </aside>
            </div>
        </div>
    </div>
</div>

{{- /* Contact Form JavaScript */ -}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('contact-submit');
            const submitText = submitBtn.querySelector('.submit-text');
            const submitLoading = submitBtn.querySelector('.submit-loading');
            const successMsg = document.getElementById('contact-success');
            const errorMsg = document.getElementById('contact-error');
            
            // Show loading state
            submitText.style.display = 'none';
            submitLoading.style.display = 'inline';
            submitBtn.disabled = true;
            
            // Hide previous messages
            successMsg.style.display = 'none';
            errorMsg.style.display = 'none';
            
            // Simulate form submission (replace with actual implementation)
            setTimeout(() => {
                // Reset button state
                submitText.style.display = 'inline';
                submitLoading.style.display = 'none';
                submitBtn.disabled = false;
                
                // Show success message
                successMsg.style.display = 'block';
                contactForm.reset();
                
                // Scroll to success message
                successMsg.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 2000);
        });
    }
});
</script>

{{- /* JSON-LD Structured Data */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "{{ .Title }}",
    "description": "{{ with .Description }}{{ . }}{{ else }}{{ .Site.Params.description }}{{ end }}",
    "url": "{{ .Permalink }}",
    "mainEntity": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "url": "{{ .Site.BaseURL }}"
        {{- with .Site.Params.contact.email -}},
        "email": "{{ . }}"
        {{- end -}}
        {{- with .Site.Params.contact.phone -}},
        "telephone": "{{ . }}"
        {{- end -}}
        {{- with .Site.Params.contact.address -}},
        "address": "{{ . }}"
        {{- end -}}
    }
}
</script>
{{- end -}}
