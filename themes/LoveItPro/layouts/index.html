{{- define "main" -}}
<div class="homepage">
    {{- /* Hero Carousel Section */ -}}
    {{- if .Site.Params.header.carousel.enable -}}
        {{- partial "components/carousel.html" . -}}
    {{- end -}}
    
    <div class="container">
        <div class="row">
            {{- /* Main Content Area */ -}}
            <div class="col-lg-{{ if .Site.Params.sidebar.enable }}8{{ else }}12{{ end }}">
                {{- /* Featured Posts Section */ -}}
                {{- if .Site.Params.home.featured.enable -}}
                    <section class="featured-posts mb-5">
                        <div class="section-header">
                            <h2 class="section-title">
                                {{ .Site.Params.home.featured.title | default (i18n "featuredPosts" | default "Featured Posts") }}
                            </h2>
                            <div class="section-divider"></div>
                        </div>
                        
                        <div class="row">
                            {{- $featured := where .Site.RegularPages "Params.featured" true -}}
                            {{- if not $featured -}}
                                {{- $featured = first (.Site.Params.home.featured.count | default 3) .Site.RegularPages -}}
                            {{- end -}}
                            
                            {{- range $featured -}}
                                <div class="col-md-6 col-lg-4 mb-4">
                                    {{- partial "components/article-card.html" (dict "page" . "featured" true) -}}
                                </div>
                            {{- end -}}
                        </div>
                    </section>
                {{- end -}}
                
                {{- /* Recent Posts Section */ -}}
                {{- if .Site.Params.home.posts.enable -}}
                    <section class="recent-posts">
                        <div class="section-header">
                            <h2 class="section-title">
                                {{ .Site.Params.home.posts.title | default (i18n "recentPosts" | default "Recent Posts") }}
                            </h2>
                            <div class="section-divider"></div>
                        </div>
                        
                        {{- /* View Toggle Buttons */ -}}
                        <div class="view-toggle mb-4">
                            <div class="btn-group" role="group" aria-label="{{ i18n "viewToggle" | default "View toggle" }}">
                                <button type="button" class="btn btn-outline-primary active" data-view="grid">
                                    <i class="fas fa-th"></i> {{ i18n "gridView" | default "Grid" }}
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-view="list">
                                    <i class="fas fa-list"></i> {{ i18n "listView" | default "List" }}
                                </button>
                            </div>
                        </div>
                        
                        {{- /* Posts Grid/List */ -}}
                        <div id="posts-container" class="posts-grid">
                            <div class="row">
                                {{- $paginator := .Paginate (first (.Site.Params.home.posts.count | default 6) .Site.RegularPages) (.Site.Params.home.posts.paginate | default 6) -}}
                                {{- range $paginator.Pages -}}
                                    <div class="col-md-6 col-lg-4 mb-4 post-item">
                                        {{- partial "components/article-card.html" (dict "page" .) -}}
                                    </div>
                                {{- end -}}
                            </div>
                        </div>
                        
                        {{- /* Pagination */ -}}
                        {{- if gt $paginator.TotalPages 1 -}}
                            <nav aria-label="{{ i18n "pagination" | default "Pagination" }}" class="mt-5">
                                {{- partial "components/pagination.html" $paginator -}}
                            </nav>
                        {{- end -}}
                        
                        {{- /* Load More Button */ -}}
                        <div class="text-center mt-4">
                            <a href="{{ "posts" | relLangURL }}" class="btn btn-primary btn-lg">
                                {{ i18n "viewAllPosts" | default "View All Posts" }}
                                <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </section>
                {{- end -}}
            </div>
            
            {{- /* Sidebar */ -}}
            {{- if .Site.Params.sidebar.enable -}}
                <div class="col-lg-4">
                    {{- partial "sidebar/sidebar.html" . -}}
                </div>
            {{- end -}}
        </div>
    </div>
</div>

{{- /* JSON-LD Structured Data */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "{{ .Site.Title }}",
    "description": "{{ .Site.Params.description }}",
    "url": "{{ .Site.BaseURL }}",
    "author": {
        "@type": "Person",
        "name": "{{ .Site.Params.author }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ .Site.Params.seo.image | absURL }}"
        }
    }
}
</script>
{{- end -}}
