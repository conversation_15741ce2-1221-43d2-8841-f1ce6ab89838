{{- /* Header Navigation */ -}}
<header class="site-header">
    {{- /* Top Navigation Bar */ -}}
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            {{- /* Brand/Logo */ -}}
            <a class="navbar-brand" href="{{ .Site.Home.RelPermalink }}">
                {{- with .Site.Params.header.logo -}}
                    <img src="{{ . | relURL }}" alt="{{ $.Site.Title }}" class="logo-img">
                {{- else -}}
                    <span class="brand-text">{{ .Site.Title }}</span>
                {{- end -}}
            </a>
            
            {{- /* Mobile Menu Toggle */ -}}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="{{ i18n "toggleNavigation" | default "Toggle navigation" }}">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            {{- /* Navigation Menu */ -}}
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {{- range .Site.Menus.main -}}
                        {{- $url := .URL | relLangURL -}}
                        {{- with .Page -}}
                            {{- $url = .RelPermalink -}}
                        {{- end -}}
                        <li class="nav-item">
                            <a class="nav-link{{ if $.IsMenuCurrent "main" . | or ($.HasMenuCurrent "main" .) | or (eq $.RelPermalink $url) }} active{{ end }}" 
                               href="{{ $url }}"{{ with .Title }} title="{{ . }}"{{ end }}{{ if (urls.Parse $url).Host }} target="_blank" rel="noopener"{{ end }}>
                                {{- .Pre | safeHTML -}}
                                {{ .Name }}
                                {{- .Post | safeHTML -}}
                            </a>
                        </li>
                    {{- end -}}
                </ul>
                
                {{- /* Right Side Navigation */ -}}
                <div class="navbar-nav ms-auto">
                    {{- /* Search Button */ -}}
                    {{- if .Site.Params.header.search.enable -}}
                        <button class="btn btn-outline-primary me-2" type="button" data-bs-toggle="modal" data-bs-target="#searchModal">
                            <i class="fas fa-search"></i>
                            <span class="d-none d-md-inline ms-1">{{ i18n "search" | default "Search" }}</span>
                        </button>
                    {{- end -}}
                    
                    {{- /* Theme Toggle */ -}}
                    <button class="btn btn-outline-secondary me-2" id="theme-toggle" title="{{ i18n "toggleTheme" | default "Toggle theme" }}">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>
                    
                    {{- /* Language Selector */ -}}
                    {{- if hugo.IsMultilingual -}}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" 
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-globe"></i>
                                <span class="d-none d-md-inline ms-1">{{ .Site.Language.LanguageName }}</span>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                {{- if eq .Kind "404" -}}
                                    {{- range .Sites -}}
                                        {{- $link := printf "%v/404.html" .LanguagePrefix -}}
                                        <li>
                                            <a class="dropdown-item{{ if eq . $.Site }} active{{ end }}" href="{{ $link }}">
                                                {{ .Language.LanguageName }}
                                            </a>
                                        </li>
                                    {{- end -}}
                                {{- else -}}
                                    {{- range .AllTranslations -}}
                                        <li>
                                            <a class="dropdown-item{{ if eq .Lang $.Lang }} active{{ end }}" href="{{ .RelPermalink }}">
                                                {{ .Language.LanguageName }}
                                            </a>
                                        </li>
                                    {{- end -}}
                                {{- end -}}
                            </ul>
                        </div>
                    {{- end -}}
                </div>
            </div>
        </div>
    </nav>
    
    {{- /* Hero Carousel (only on homepage) */ -}}
    {{- if and .IsHome .Site.Params.header.carousel.enable -}}
        {{- partial "components/carousel.html" . -}}
    {{- end -}}
</header>
