{{- /* Pagination Component */ -}}
{{- $paginator := . -}}

{{- if gt $paginator.TotalPages 1 -}}
<nav aria-label="{{ i18n "pagination" | default "Pagination" }}">
    <ul class="pagination justify-content-center">
        {{- /* First Page */ -}}
        {{- if ne $paginator.PageNumber 1 -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.First.URL }}" aria-label="{{ i18n "firstPage" | default "First page" }}">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            </li>
        {{- end -}}
        
        {{- /* Previous Page */ -}}
        {{- if $paginator.HasPrev -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.Prev.URL }}" aria-label="{{ i18n "previousPage" | default "Previous page" }}">
                    <i class="fas fa-angle-left"></i>
                </a>
            </li>
        {{- else -}}
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-angle-left"></i>
                </span>
            </li>
        {{- end -}}
        
        {{- /* Page Numbers */ -}}
        {{- $currentPage := $paginator.PageNumber -}}
        {{- $totalPages := $paginator.TotalPages -}}
        {{- $startPage := 1 -}}
        {{- $endPage := $totalPages -}}
        
        {{- /* Calculate visible page range */ -}}
        {{- if gt $totalPages 7 -}}
            {{- if le $currentPage 4 -}}
                {{- $endPage = 5 -}}
            {{- else if ge $currentPage (sub $totalPages 3) -}}
                {{- $startPage = sub $totalPages 4 -}}
            {{- else -}}
                {{- $startPage = sub $currentPage 2 -}}
                {{- $endPage = add $currentPage 2 -}}
            {{- end -}}
        {{- end -}}
        
        {{- /* Show first page and ellipsis if needed */ -}}
        {{- if gt $startPage 1 -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.First.URL }}">1</a>
            </li>
            {{- if gt $startPage 2 -}}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {{- end -}}
        {{- end -}}
        
        {{- /* Page number links */ -}}
        {{- range seq $startPage $endPage -}}
            {{- if eq . $currentPage -}}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ . }}</span>
                </li>
            {{- else -}}
                <li class="page-item">
                    <a class="page-link" href="{{ (index $paginator.Pagers (sub . 1)).URL }}">{{ . }}</a>
                </li>
            {{- end -}}
        {{- end -}}
        
        {{- /* Show last page and ellipsis if needed */ -}}
        {{- if lt $endPage $totalPages -}}
            {{- if lt $endPage (sub $totalPages 1) -}}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {{- end -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.Last.URL }}">{{ $totalPages }}</a>
            </li>
        {{- end -}}
        
        {{- /* Next Page */ -}}
        {{- if $paginator.HasNext -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.Next.URL }}" aria-label="{{ i18n "nextPage" | default "Next page" }}">
                    <i class="fas fa-angle-right"></i>
                </a>
            </li>
        {{- else -}}
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-angle-right"></i>
                </span>
            </li>
        {{- end -}}
        
        {{- /* Last Page */ -}}
        {{- if ne $paginator.PageNumber $paginator.TotalPages -}}
            <li class="page-item">
                <a class="page-link" href="{{ $paginator.Last.URL }}" aria-label="{{ i18n "lastPage" | default "Last page" }}">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            </li>
        {{- end -}}
    </ul>
    
    {{- /* Pagination Info */ -}}
    <div class="pagination-info text-center mt-3">
        <small class="text-muted">
            {{ i18n "showingPage" | default "Showing page" }} {{ $paginator.PageNumber }} {{ i18n "of" | default "of" }} {{ $paginator.TotalPages }}
            ({{ i18n "totalPosts" | default "total" }}: {{ $paginator.TotalNumberOfElements }})
        </small>
    </div>
</nav>
{{- end -}}
