{{- /* Article Card Component */ -}}
{{- $page := .page -}}
{{- $featured := .featured | default false -}}
{{- $compact := .compact | default false -}}

<article class="article-card{{ if $featured }} featured{{ end }}{{ if $compact }} compact{{ end }}">
    <div class="card h-100 shadow-sm">
        {{- /* Card Image */ -}}
        {{- if and .Site.Params.article.card.showImage $page.Params.image -}}
            <div class="card-img-container">
                <a href="{{ $page.RelPermalink }}" class="card-img-link">
                    <img src="{{ $page.Params.image | relURL }}" 
                         alt="{{ $page.Title }}" 
                         class="card-img-top"
                         loading="lazy">
                </a>
                
                {{- /* Featured Badge */ -}}
                {{- if $featured -}}
                    <div class="featured-badge">
                        <i class="fas fa-star"></i>
                        {{ i18n "featured" | default "Featured" }}
                    </div>
                {{- end -}}
                
                {{- /* Category Badge */ -}}
                {{- with $page.Params.categories -}}
                    <div class="category-badge">
                        <a href="{{ printf "/categories/%s/" (index . 0 | urlize) | relLangURL }}">
                            {{ index . 0 }}
                        </a>
                    </div>
                {{- end -}}
            </div>
        {{- end -}}
        
        <div class="card-body d-flex flex-column">
            {{- /* Card Header */ -}}
            <div class="card-header-info">
                {{- /* Title */ -}}
                <h3 class="card-title{{ if $compact }} h6{{ else }} h5{{ end }}">
                    <a href="{{ $page.RelPermalink }}" class="text-decoration-none">
                        {{ $page.Title }}
                    </a>
                </h3>
                
                {{- /* Meta Information */ -}}
                <div class="card-meta">
                    {{- if .Site.Params.article.card.showDate -}}
                        <span class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <time datetime="{{ $page.Date.Format "2006-01-02T15:04:05Z07:00" }}">
                                {{ $page.Date.Format (.Site.Params.dateFormat | default "Jan 2, 2006") }}
                            </time>
                        </span>
                    {{- end -}}
                    
                    {{- if and .Site.Params.article.card.showAuthor ($page.Params.author | default .Site.Params.author) -}}
                        <span class="meta-item">
                            <i class="fas fa-user"></i>
                            {{ $page.Params.author | default .Site.Params.author }}
                        </span>
                    {{- end -}}
                    
                    {{- if and .Site.Params.article.card.showReadingTime .Site.Params.article.readingTime.enable -}}
                        <span class="meta-item">
                            <i class="fas fa-clock"></i>
                            {{ partial "components/reading-time.html" $page }}
                        </span>
                    {{- end -}}
                </div>
            </div>
            
            {{- /* Card Content */ -}}
            {{- if and .Site.Params.article.card.showExcerpt (not $compact) -}}
                <div class="card-excerpt mt-2 flex-grow-1">
                    <p class="text-muted">
                        {{ $page.Summary | truncate 120 }}
                    </p>
                </div>
            {{- end -}}
            
            {{- /* Card Footer */ -}}
            <div class="card-footer-info mt-auto">
                {{- /* Tags */ -}}
                {{- if and .Site.Params.article.card.showTags $page.Params.tags (not $compact) -}}
                    <div class="card-tags mb-2">
                        {{- range first 3 $page.Params.tags -}}
                            <a href="{{ printf "/tags/%s/" (. | urlize) | relLangURL }}" class="tag-link">
                                <span class="badge bg-light text-dark">#{{ . }}</span>
                            </a>
                        {{- end -}}
                        {{- if gt (len $page.Params.tags) 3 -}}
                            <span class="badge bg-secondary">+{{ sub (len $page.Params.tags) 3 }}</span>
                        {{- end -}}
                    </div>
                {{- end -}}
                
                {{- /* Read More Button */ -}}
                <div class="card-actions">
                    <a href="{{ $page.RelPermalink }}" class="btn btn-outline-primary btn-sm">
                        {{ i18n "readMore" | default "Read More" }}
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                    
                    {{- /* Additional Actions */ -}}
                    <div class="card-actions-extra">
                        {{- /* Bookmark Button */ -}}
                        <button class="btn btn-outline-secondary btn-sm bookmark-btn" 
                                data-url="{{ $page.RelPermalink }}" 
                                data-title="{{ $page.Title }}"
                                title="{{ i18n "bookmark" | default "Bookmark" }}">
                            <i class="far fa-bookmark"></i>
                        </button>
                        
                        {{- /* Share Button */ -}}
                        <div class="dropdown d-inline">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                    type="button" 
                                    id="shareDropdown{{ $page.File.UniqueID }}" 
                                    data-bs-toggle="dropdown" 
                                    aria-expanded="false"
                                    title="{{ i18n "share" | default "Share" }}">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="shareDropdown{{ $page.File.UniqueID }}">
                                <li>
                                    <a class="dropdown-item" href="#" 
                                       onclick="shareToTwitter('{{ $page.Permalink }}', '{{ $page.Title }}')">
                                        <i class="fab fa-twitter"></i> Twitter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" 
                                       onclick="shareToFacebook('{{ $page.Permalink }}')">
                                        <i class="fab fa-facebook"></i> Facebook
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" 
                                       onclick="copyToClipboard('{{ $page.Permalink }}')">
                                        <i class="fas fa-link"></i> {{ i18n "copyLink" | default "Copy Link" }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {{- /* Card Overlay for Hover Effects */ -}}
        <div class="card-overlay"></div>
    </div>
</article>
