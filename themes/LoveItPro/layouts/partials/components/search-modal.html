{{- /* Search Modal Component */ -}}
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">
                    <i class="fas fa-search me-2"></i>
                    {{ i18n "search" | default "Search" }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ i18n "close" | default "Close" }}"></button>
            </div>
            <div class="modal-body">
                {{- /* Search Input */ -}}
                <div class="search-input-container">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search-input" 
                               placeholder="{{ .Site.Params.header.search.placeholder | default (i18n "searchPlaceholder" | default "Search articles...") }}"
                               aria-label="{{ i18n "search" | default "Search" }}"
                               autocomplete="off">
                        <button class="btn btn-outline-secondary" type="button" id="clear-search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                {{- /* Search Filters */ -}}
                <div class="search-filters mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-select" id="search-category">
                                <option value="">{{ i18n "allCategories" | default "All Categories" }}</option>
                                {{- range .Site.Taxonomies.categories -}}
                                    <option value="{{ .Page.Title }}">{{ .Page.Title }} ({{ .Count }})</option>
                                {{- end -}}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="search-tag">
                                <option value="">{{ i18n "allTags" | default "All Tags" }}</option>
                                {{- range .Site.Taxonomies.tags -}}
                                    <option value="{{ .Page.Title }}">{{ .Page.Title }} ({{ .Count }})</option>
                                {{- end -}}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="search-sort">
                                <option value="relevance">{{ i18n "sortByRelevance" | default "Relevance" }}</option>
                                <option value="date">{{ i18n "sortByDate" | default "Date" }}</option>
                                <option value="title">{{ i18n "sortByTitle" | default "Title" }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                {{- /* Search Results */ -}}
                <div id="search-results" class="search-results mt-4">
                    {{- /* Initial State */ -}}
                    <div class="search-initial text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{{ i18n "searchInitial" | default "Start typing to search..." }}</h5>
                        <p class="text-muted">{{ i18n "searchHint" | default "Search through all articles, categories, and tags" }}</p>
                    </div>
                    
                    {{- /* Loading State */ -}}
                    <div class="search-loading text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ i18n "loading" | default "Loading..." }}</span>
                        </div>
                        <p class="mt-3 text-muted">{{ i18n "searching" | default "Searching..." }}</p>
                    </div>
                    
                    {{- /* No Results State */ -}}
                    <div class="search-no-results text-center py-5" style="display: none;">
                        <i class="fas fa-search-minus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">{{ i18n "noSearchResults" | default "No results found" }}</h5>
                        <p class="text-muted">{{ i18n "noSearchResultsHint" | default "Try different keywords or check your spelling" }}</p>
                    </div>
                    
                    {{- /* Results Container */ -}}
                    <div id="search-results-list" class="search-results-list"></div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="search-stats">
                    <small class="text-muted" id="search-stats"></small>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ i18n "close" | default "Close" }}
                </button>
            </div>
        </div>
    </div>
</div>

{{- /* Search Data (JSON) */ -}}
<script id="search-data" type="application/json">
[
    {{- range $index, $page := .Site.RegularPages -}}
        {{- if gt $index 0 }},{{ end }}
        {
            "title": {{ $page.Title | jsonify }},
            "url": {{ $page.RelPermalink | jsonify }},
            "content": {{ $page.Plain | jsonify }},
            "summary": {{ $page.Summary | jsonify }},
            "date": {{ $page.Date.Format "2006-01-02" | jsonify }},
            "categories": {{ $page.Params.categories | jsonify }},
            "tags": {{ $page.Params.tags | jsonify }},
            "author": {{ $page.Params.author | default .Site.Params.author | jsonify }}
        }
    {{- end -}}
]
</script>
