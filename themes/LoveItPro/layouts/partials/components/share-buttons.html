{{- /* Share Buttons Component */ -}}
<div class="share-buttons">
    <div class="share-buttons-list">
        {{- /* Twitter */ -}}
        <a href="javascript:void(0)" 
           onclick="shareToTwitter('{{ .Permalink }}', '{{ .Title }}')"
           class="share-btn share-twitter"
           title="{{ i18n "shareOnTwitter" | default "Share on Twitter" }}">
            <i class="fab fa-twitter"></i>
            <span class="share-text">Twitter</span>
        </a>
        
        {{- /* Facebook */ -}}
        <a href="javascript:void(0)" 
           onclick="shareToFacebook('{{ .Permalink }}')"
           class="share-btn share-facebook"
           title="{{ i18n "shareOnFacebook" | default "Share on Facebook" }}">
            <i class="fab fa-facebook-f"></i>
            <span class="share-text">Facebook</span>
        </a>
        
        {{- /* LinkedIn */ -}}
        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ .Permalink | urlize }}"
           target="_blank"
           rel="noopener"
           class="share-btn share-linkedin"
           title="{{ i18n "shareOnLinkedIn" | default "Share on LinkedIn" }}">
            <i class="fab fa-linkedin-in"></i>
            <span class="share-text">LinkedIn</span>
        </a>
        
        {{- /* WhatsApp */ -}}
        <a href="https://wa.me/?text={{ .Title | urlize }}%20{{ .Permalink | urlize }}"
           target="_blank"
           rel="noopener"
           class="share-btn share-whatsapp"
           title="{{ i18n "shareOnWhatsApp" | default "Share on WhatsApp" }}">
            <i class="fab fa-whatsapp"></i>
            <span class="share-text">WhatsApp</span>
        </a>
        
        {{- /* Telegram */ -}}
        <a href="https://t.me/share/url?url={{ .Permalink | urlize }}&text={{ .Title | urlize }}"
           target="_blank"
           rel="noopener"
           class="share-btn share-telegram"
           title="{{ i18n "shareOnTelegram" | default "Share on Telegram" }}">
            <i class="fab fa-telegram-plane"></i>
            <span class="share-text">Telegram</span>
        </a>
        
        {{- /* Reddit */ -}}
        <a href="https://reddit.com/submit?url={{ .Permalink | urlize }}&title={{ .Title | urlize }}"
           target="_blank"
           rel="noopener"
           class="share-btn share-reddit"
           title="{{ i18n "shareOnReddit" | default "Share on Reddit" }}">
            <i class="fab fa-reddit-alien"></i>
            <span class="share-text">Reddit</span>
        </a>
        
        {{- /* Email */ -}}
        <a href="mailto:?subject={{ .Title | urlize }}&body={{ .Permalink | urlize }}"
           class="share-btn share-email"
           title="{{ i18n "shareViaEmail" | default "Share via Email" }}">
            <i class="fas fa-envelope"></i>
            <span class="share-text">{{ i18n "email" | default "Email" }}</span>
        </a>
        
        {{- /* Copy Link */ -}}
        <a href="javascript:void(0)" 
           onclick="copyToClipboard('{{ .Permalink }}')"
           class="share-btn share-copy"
           title="{{ i18n "copyLink" | default "Copy Link" }}">
            <i class="fas fa-link"></i>
            <span class="share-text">{{ i18n "copyLink" | default "Copy Link" }}</span>
        </a>
    </div>
    
    {{- /* Share Count (if available) */ -}}
    {{- $uniqueID := .RelPermalink | md5 -}}
    <div class="share-count" id="share-count-{{ $uniqueID }}" style="display: none;">
        <small class="text-muted">
            <i class="fas fa-share-alt"></i>
            <span id="share-count-number">0</span> {{ i18n "shares" | default "shares" }}
        </small>
    </div>
</div>

<style>
.share-buttons {
    margin: 1rem 0;
}

.share-buttons-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
    min-width: 100px;
    justify-content: center;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    color: white;
}

.share-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.share-twitter {
    background-color: #1da1f2;
}

.share-twitter:hover {
    background-color: #0d8bd9;
}

.share-facebook {
    background-color: #1877f2;
}

.share-facebook:hover {
    background-color: #166fe5;
}

.share-linkedin {
    background-color: #0077b5;
}

.share-linkedin:hover {
    background-color: #005885;
}

.share-whatsapp {
    background-color: #25d366;
}

.share-whatsapp:hover {
    background-color: #128c7e;
}

.share-telegram {
    background-color: #0088cc;
}

.share-telegram:hover {
    background-color: #006699;
}

.share-reddit {
    background-color: #ff4500;
}

.share-reddit:hover {
    background-color: #cc3700;
}

.share-email {
    background-color: #6c757d;
}

.share-email:hover {
    background-color: #545b62;
}

.share-copy {
    background-color: #28a745;
}

.share-copy:hover {
    background-color: #1e7e34;
}

.share-count {
    margin-top: 0.5rem;
    text-align: center;
}

@media (max-width: 576px) {
    .share-btn {
        min-width: auto;
        padding: 0.5rem;
    }
    
    .share-text {
        display: none;
    }
    
    .share-btn i {
        margin-right: 0;
    }
}
</style>
