{{- /* Comments Component */ -}}
{{- if .Site.Params.comments.enable -}}
<div class="comments-section">
    <h3 class="comments-title">
        <i class="fas fa-comments"></i>
        {{ i18n "comments" | default "Comments" }}
    </h3>
    
    {{- $provider := .Site.Params.comments.provider | default "disqus" -}}
    
    {{- if eq $provider "disqus" -}}
        {{- /* Disqus Comments */ -}}
        {{- if .Site.Params.comments.disqus.shortname -}}
            <div id="disqus_thread"></div>
            <script>
                var disqus_config = function () {
                    this.page.url = "{{ .Permalink }}";
                    this.page.identifier = "{{ .RelPermalink }}";
                    this.page.title = "{{ .Title }}";
                };
                
                (function() {
                    var d = document, s = d.createElement('script');
                    s.src = 'https://{{ .Site.Params.comments.disqus.shortname }}.disqus.com/embed.js';
                    s.setAttribute('data-timestamp', +new Date());
                    (d.head || d.body).appendChild(s);
                })();
            </script>
            <noscript>
                Please enable JavaScript to view the 
                <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a>
            </noscript>
        {{- end -}}
        
    {{- else if eq $provider "gitalk" -}}
        {{- /* Gitalk Comments */ -}}
        {{- if .Site.Params.comments.gitalk.clientID -}}
            <div id="gitalk-container"></div>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.css">
            <script src="https://cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.min.js"></script>
            <script>
                const gitalk = new Gitalk({
                    clientID: '{{ .Site.Params.comments.gitalk.clientID }}',
                    clientSecret: '{{ .Site.Params.comments.gitalk.clientSecret }}',
                    repo: '{{ .Site.Params.comments.gitalk.repo }}',
                    owner: '{{ .Site.Params.comments.gitalk.owner }}',
                    admin: [{{ range .Site.Params.comments.gitalk.admin }}'{{ . }}',{{ end }}],
                    id: '{{ .RelPermalink | md5 }}',
                    title: '{{ .Title }}',
                    body: '{{ .Permalink }}',
                    distractionFreeMode: {{ .Site.Params.comments.gitalk.distractionFreeMode | default false }}
                });
                gitalk.render('gitalk-container');
            </script>
        {{- end -}}
        
    {{- else if eq $provider "valine" -}}
        {{- /* Valine Comments */ -}}
        {{- if .Site.Params.comments.valine.appId -}}
            <div id="valine-comments"></div>
            <script src="https://cdn.jsdelivr.net/npm/valine@1/dist/Valine.min.js"></script>
            <script>
                new Valine({
                    el: '#valine-comments',
                    appId: '{{ .Site.Params.comments.valine.appId }}',
                    appKey: '{{ .Site.Params.comments.valine.appKey }}',
                    placeholder: '{{ .Site.Params.comments.valine.placeholder | default "Just go go" }}',
                    avatar: '{{ .Site.Params.comments.valine.avatar | default "mp" }}',
                    pageSize: {{ .Site.Params.comments.valine.pageSize | default 10 }},
                    lang: '{{ .Site.Language.Lang }}',
                    visitor: {{ .Site.Params.comments.valine.visitor | default false }},
                    recordIP: {{ .Site.Params.comments.valine.recordIP | default false }},
                    enableQQ: {{ .Site.Params.comments.valine.enableQQ | default false }}
                });
            </script>
        {{- end -}}
        
    {{- else if eq $provider "utterances" -}}
        {{- /* Utterances Comments */ -}}
        {{- if .Site.Params.comments.utterances.repo -}}
            <script src="https://utteranc.es/client.js"
                    repo="{{ .Site.Params.comments.utterances.repo }}"
                    issue-term="{{ .Site.Params.comments.utterances.issueTerm | default "pathname" }}"
                    theme="{{ .Site.Params.comments.utterances.theme | default "github-light" }}"
                    crossorigin="anonymous"
                    async>
            </script>
        {{- end -}}
        
    {{- else if eq $provider "giscus" -}}
        {{- /* Giscus Comments */ -}}
        {{- if .Site.Params.comments.giscus.repo -}}
            <script src="https://giscus.app/client.js"
                    data-repo="{{ .Site.Params.comments.giscus.repo }}"
                    data-repo-id="{{ .Site.Params.comments.giscus.repoId }}"
                    data-category="{{ .Site.Params.comments.giscus.category }}"
                    data-category-id="{{ .Site.Params.comments.giscus.categoryId }}"
                    data-mapping="{{ .Site.Params.comments.giscus.mapping | default "pathname" }}"
                    data-strict="{{ .Site.Params.comments.giscus.strict | default "0" }}"
                    data-reactions-enabled="{{ .Site.Params.comments.giscus.reactionsEnabled | default "1" }}"
                    data-emit-metadata="{{ .Site.Params.comments.giscus.emitMetadata | default "0" }}"
                    data-input-position="{{ .Site.Params.comments.giscus.inputPosition | default "bottom" }}"
                    data-theme="{{ .Site.Params.comments.giscus.theme | default "light" }}"
                    data-lang="{{ .Site.Language.Lang }}"
                    crossorigin="anonymous"
                    async>
            </script>
        {{- end -}}
        
    {{- else -}}
        {{- /* Custom Comments */ -}}
        <div class="custom-comments">
            <p class="text-muted">
                {{ i18n "commentsNotConfigured" | default "Comments are not configured. Please check your site configuration." }}
            </p>
        </div>
    {{- end -}}
    
    {{- /* Comments Guidelines */ -}}
    <div class="comments-guidelines mt-4">
        <h5>{{ i18n "commentsGuidelines" | default "Comment Guidelines" }}</h5>
        <ul class="list-unstyled">
            <li><i class="fas fa-check text-success"></i> {{ i18n "guidelineRespectful" | default "Be respectful and constructive" }}</li>
            <li><i class="fas fa-check text-success"></i> {{ i18n "guidelineRelevant" | default "Keep comments relevant to the topic" }}</li>
            <li><i class="fas fa-times text-danger"></i> {{ i18n "guidelineNoSpam" | default "No spam or promotional content" }}</li>
            <li><i class="fas fa-times text-danger"></i> {{ i18n "guidelineNoOffensive" | default "No offensive or inappropriate language" }}</li>
        </ul>
    </div>
</div>
{{- end -}}
