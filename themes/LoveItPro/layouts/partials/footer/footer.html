{{- /* Site Footer */ -}}
<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <div class="row">
                {{- /* About Section */ -}}
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h5>{{ .Site.Title }}</h5>
                        <p>{{ .Site.Params.description | default "A modern Hugo blog theme with advanced features." }}</p>
                        
                        {{- /* Social Links */ -}}
                        {{- if .Site.Params.social -}}
                            <div class="footer-social">
                                {{- range $name, $url := .Site.Params.social -}}
                                    {{- if $url -}}
                                        {{- if eq $name "Email" -}}
                                            <a href="mailto:{{ $url }}" title="{{ $name }}" target="_blank" rel="noopener">
                                                <i class="fas fa-envelope"></i>
                                            </a>
                                        {{- else if eq $name "RSS" -}}
                                            {{- if $url -}}
                                                <a href="{{ "index.xml" | relLangURL }}" title="{{ $name }}" target="_blank" rel="noopener">
                                                    <i class="fas fa-rss"></i>
                                                </a>
                                            {{- end -}}
                                        {{- else -}}
                                            <a href="{{ $url }}" title="{{ $name }}" target="_blank" rel="noopener">
                                                {{- if eq $name "GitHub" -}}
                                                    <i class="fab fa-github"></i>
                                                {{- else if eq $name "Twitter" -}}
                                                    <i class="fab fa-twitter"></i>
                                                {{- else if eq $name "LinkedIn" -}}
                                                    <i class="fab fa-linkedin"></i>
                                                {{- else if eq $name "Facebook" -}}
                                                    <i class="fab fa-facebook"></i>
                                                {{- else if eq $name "Instagram" -}}
                                                    <i class="fab fa-instagram"></i>
                                                {{- else if eq $name "YouTube" -}}
                                                    <i class="fab fa-youtube"></i>
                                                {{- else if eq $name "Weibo" -}}
                                                    <i class="fab fa-weibo"></i>
                                                {{- else if eq $name "Zhihu" -}}
                                                    <i class="fab fa-zhihu"></i>
                                                {{- else -}}
                                                    <i class="fas fa-link"></i>
                                                {{- end -}}
                                            </a>
                                        {{- end -}}
                                    {{- end -}}
                                {{- end -}}
                            </div>
                        {{- end -}}
                    </div>
                </div>
                
                {{- /* Quick Links */ -}}
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h5>{{ i18n "quickLinks" | default "Quick Links" }}</h5>
                        <ul>
                            <li><a href="{{ .Site.Home.RelPermalink }}">{{ i18n "home" | default "Home" }}</a></li>
                            <li><a href="{{ "posts" | relLangURL }}">{{ i18n "allPosts" | default "All Posts" }}</a></li>
                            <li><a href="{{ "categories" | relLangURL }}">{{ i18n "categories" | default "Categories" }}</a></li>
                            <li><a href="{{ "tags" | relLangURL }}">{{ i18n "tags" | default "Tags" }}</a></li>
                            <li><a href="{{ "about" | relLangURL }}">{{ i18n "about" | default "About" }}</a></li>
                            <li><a href="{{ "contact" | relLangURL }}">{{ i18n "contact" | default "Contact" }}</a></li>
                        </ul>
                    </div>
                </div>
                
                {{- /* Categories */ -}}
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h5>{{ i18n "categories" | default "Categories" }}</h5>
                        <ul>
                            {{- range first 6 .Site.Taxonomies.categories.ByCount -}}
                                <li>
                                    <a href="{{ .Page.RelPermalink }}">
                                        {{ .Page.Title }} ({{ .Count }})
                                    </a>
                                </li>
                            {{- end -}}
                        </ul>
                    </div>
                </div>
                
                {{- /* Recent Posts */ -}}
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h5>{{ i18n "recentPosts" | default "Recent Posts" }}</h5>
                        <div class="footer-posts">
                            {{- range first 3 .Site.RegularPages -}}
                                <article class="footer-post">
                                    <h6 class="post-title">
                                        <a href="{{ .RelPermalink }}">{{ .Title }}</a>
                                    </h6>
                                    <div class="post-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt"></i>
                                            {{ .Date.Format (.Site.Params.dateFormat | default "Jan 2, 2006") }}
                                        </small>
                                    </div>
                                </article>
                            {{- end -}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {{- /* Footer Bottom */ -}}
        <div class="footer-bottom">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="copyright">
                        &copy; {{ now.Year }} {{ .Site.Title }}. 
                        {{ i18n "allRightsReserved" | default "All rights reserved." }}
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="powered-by">
                        {{ i18n "poweredBy" | default "Powered by" }} 
                        <a href="https://gohugo.io/" target="_blank" rel="noopener">Hugo</a> & 
                        <a href="https://github.com/wenhaofree/LoveItPro" target="_blank" rel="noopener">LoveItPro</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>
