{{- /* Social Media Widget */ -}}
{{- if .Site.Params.social -}}
<div class="sidebar-widget social-widget">
    <div class="widget-header">
        <h4 class="widget-title">
            <i class="fas fa-share-alt"></i>
            {{ .Site.Params.sidebar.social.title | default (i18n "followMe" | default "Follow Me") }}
        </h4>
    </div>
    <div class="widget-content">
        <div class="social-links">
            {{- range $name, $url := .Site.Params.social -}}
                {{- if $url -}}
                    {{- $iconClass := "" -}}
                    {{- $displayName := $name -}}
                    {{- $bgColor := "" -}}
                    
                    {{- if eq $name "GitHub" -}}
                        {{- $iconClass = "fab fa-github" -}}
                        {{- $bgColor = "bg-dark" -}}
                    {{- else if eq $name "Twitter" -}}
                        {{- $iconClass = "fab fa-twitter" -}}
                        {{- $bgColor = "bg-info" -}}
                    {{- else if eq $name "LinkedIn" -}}
                        {{- $iconClass = "fab fa-linkedin" -}}
                        {{- $bgColor = "bg-primary" -}}
                    {{- else if eq $name "Facebook" -}}
                        {{- $iconClass = "fab fa-facebook" -}}
                        {{- $bgColor = "bg-primary" -}}
                    {{- else if eq $name "Instagram" -}}
                        {{- $iconClass = "fab fa-instagram" -}}
                        {{- $bgColor = "bg-danger" -}}
                    {{- else if eq $name "YouTube" -}}
                        {{- $iconClass = "fab fa-youtube" -}}
                        {{- $bgColor = "bg-danger" -}}
                    {{- else if eq $name "Weibo" -}}
                        {{- $iconClass = "fab fa-weibo" -}}
                        {{- $bgColor = "bg-warning" -}}
                    {{- else if eq $name "Zhihu" -}}
                        {{- $iconClass = "fab fa-zhihu" -}}
                        {{- $bgColor = "bg-primary" -}}
                    {{- else if eq $name "Email" -}}
                        {{- $iconClass = "fas fa-envelope" -}}
                        {{- $bgColor = "bg-secondary" -}}
                        {{- $displayName = i18n "email" | default "Email" -}}
                    {{- else if eq $name "RSS" -}}
                        {{- $iconClass = "fas fa-rss" -}}
                        {{- $bgColor = "bg-warning" -}}
                    {{- else -}}
                        {{- $iconClass = "fas fa-link" -}}
                        {{- $bgColor = "bg-secondary" -}}
                    {{- end -}}
                    
                    <div class="social-link-item">
                        {{- if eq $name "Email" -}}
                            <a href="mailto:{{ $url }}" class="social-link {{ $bgColor }}" title="{{ $displayName }}" target="_blank" rel="noopener">
                        {{- else if eq $name "RSS" -}}
                            {{- if $url -}}
                                <a href="{{ "index.xml" | relLangURL }}" class="social-link {{ $bgColor }}" title="{{ $displayName }}" target="_blank" rel="noopener">
                            {{- else -}}
                                <a href="{{ "index.xml" | relLangURL }}" class="social-link {{ $bgColor }}" title="{{ $displayName }}" target="_blank" rel="noopener">
                            {{- end -}}
                        {{- else -}}
                            <a href="{{ $url }}" class="social-link {{ $bgColor }}" title="{{ $displayName }}" target="_blank" rel="noopener">
                        {{- end -}}
                            <div class="social-icon">
                                <i class="{{ $iconClass }}"></i>
                            </div>
                            <div class="social-info">
                                <div class="social-name">{{ $displayName }}</div>
                                <div class="social-description">
                                    {{- if eq $name "GitHub" -}}
                                        {{ i18n "followOnGitHub" | default "Follow on GitHub" }}
                                    {{- else if eq $name "Twitter" -}}
                                        {{ i18n "followOnTwitter" | default "Follow on Twitter" }}
                                    {{- else if eq $name "LinkedIn" -}}
                                        {{ i18n "connectOnLinkedIn" | default "Connect on LinkedIn" }}
                                    {{- else if eq $name "Facebook" -}}
                                        {{ i18n "likeOnFacebook" | default "Like on Facebook" }}
                                    {{- else if eq $name "Instagram" -}}
                                        {{ i18n "followOnInstagram" | default "Follow on Instagram" }}
                                    {{- else if eq $name "YouTube" -}}
                                        {{ i18n "subscribeOnYouTube" | default "Subscribe on YouTube" }}
                                    {{- else if eq $name "Weibo" -}}
                                        {{ i18n "followOnWeibo" | default "关注微博" }}
                                    {{- else if eq $name "Zhihu" -}}
                                        {{ i18n "followOnZhihu" | default "关注知乎" }}
                                    {{- else if eq $name "Email" -}}
                                        {{ i18n "sendEmail" | default "Send Email" }}
                                    {{- else if eq $name "RSS" -}}
                                        {{ i18n "subscribeRSS" | default "Subscribe RSS" }}
                                    {{- else -}}
                                        {{ i18n "visitWebsite" | default "Visit Website" }}
                                    {{- end -}}
                                </div>
                            </div>
                            <div class="social-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>
                {{- end -}}
            {{- end -}}
        </div>
        
        {{- /* Social Stats */ -}}
        <div class="social-stats mt-3">
            <div class="row text-center">
                <div class="col-4">
                    <div class="stat-item">
                        <div class="stat-number" id="github-followers">-</div>
                        <div class="stat-label">{{ i18n "followers" | default "Followers" }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="stat-item">
                        <div class="stat-number" id="twitter-followers">-</div>
                        <div class="stat-label">{{ i18n "tweets" | default "Tweets" }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="stat-item">
                        <div class="stat-number">{{ len .Site.RegularPages }}</div>
                        <div class="stat-label">{{ i18n "posts" | default "Posts" }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{- end -}}
