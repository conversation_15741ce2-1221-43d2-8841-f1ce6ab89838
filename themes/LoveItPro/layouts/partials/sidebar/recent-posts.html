{{- /* Recent Posts Widget */ -}}
{{- $recentPosts := first 5 .Site.RegularPages -}}

{{- if $recentPosts -}}
<div class="sidebar-widget recent-posts-widget">
    <div class="widget-header">
        <h4 class="widget-title">
            <i class="fas fa-clock"></i>
            {{ i18n "recentPosts" | default "Recent Posts" }}
        </h4>
    </div>
    <div class="widget-content">
        <div class="recent-posts-list">
            {{- range $recentPosts -}}
                <article class="recent-post-item">
                    <div class="post-content">
                        <h6 class="post-title">
                            <a href="{{ .RelPermalink }}" class="text-decoration-none">
                                {{ .Title }}
                            </a>
                        </h6>
                        <div class="post-meta">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt"></i>
                                {{ .Date.Format (.Site.Params.dateFormat | default "Jan 2, 2006") }}
                            </small>
                        </div>
                    </div>
                </article>
            {{- end -}}
        </div>
    </div>
</div>
{{- end -}}
