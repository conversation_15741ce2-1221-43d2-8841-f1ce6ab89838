{{- /* About Widget */ -}}
<div class="sidebar-widget about-widget">
    <div class="widget-header">
        <h4 class="widget-title">
            {{ .Site.Params.sidebar.about.title | default (i18n "aboutAuthor" | default "About Author") }}
        </h4>
    </div>
    <div class="widget-content">
        <div class="about-content text-center">
            {{- /* Avatar */ -}}
            {{- with .Site.Params.sidebar.about.avatar | default .Site.Params.avatar -}}
                <div class="about-avatar mb-3">
                    <img src="{{ . | relURL }}" alt="{{ $.Site.Params.author }}" class="avatar-img rounded-circle">
                </div>
            {{- end -}}
            
            {{- /* Author Name */ -}}
            <h5 class="about-name">{{ .Site.Params.author }}</h5>
            
            {{- /* Description */ -}}
            {{- with .Site.Params.sidebar.about.description -}}
                <p class="about-description text-muted">{{ . }}</p>
            {{- end -}}
            
            {{- /* Stats */ -}}
            <div class="about-stats">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-number">{{ len .Site.RegularPages }}</div>
                            <div class="stat-label">{{ i18n "posts" | default "Posts" }}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-number">{{ len .Site.Taxonomies.categories }}</div>
                            <div class="stat-label">{{ i18n "categories" | default "Categories" }}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <div class="stat-number">{{ len .Site.Taxonomies.tags }}</div>
                            <div class="stat-label">{{ i18n "tags" | default "Tags" }}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            {{- /* Social Links */ -}}
            {{- if .Site.Params.social -}}
                <div class="about-social mt-3">
                    {{- range $name, $url := .Site.Params.social -}}
                        {{- if $url -}}
                            {{- if eq $name "Email" -}}
                                <a href="mailto:{{ $url }}" class="social-link" title="{{ $name }}" target="_blank" rel="noopener">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            {{- else if eq $name "RSS" -}}
                                {{- if $url -}}
                                    <a href="{{ "index.xml" | relLangURL }}" class="social-link" title="{{ $name }}" target="_blank" rel="noopener">
                                        <i class="fas fa-rss"></i>
                                    </a>
                                {{- end -}}
                            {{- else -}}
                                <a href="{{ $url }}" class="social-link" title="{{ $name }}" target="_blank" rel="noopener">
                                    {{- if eq $name "GitHub" -}}
                                        <i class="fab fa-github"></i>
                                    {{- else if eq $name "Twitter" -}}
                                        <i class="fab fa-twitter"></i>
                                    {{- else if eq $name "LinkedIn" -}}
                                        <i class="fab fa-linkedin"></i>
                                    {{- else if eq $name "Facebook" -}}
                                        <i class="fab fa-facebook"></i>
                                    {{- else if eq $name "Instagram" -}}
                                        <i class="fab fa-instagram"></i>
                                    {{- else if eq $name "YouTube" -}}
                                        <i class="fab fa-youtube"></i>
                                    {{- else if eq $name "Weibo" -}}
                                        <i class="fab fa-weibo"></i>
                                    {{- else if eq $name "Zhihu" -}}
                                        <i class="fab fa-zhihu"></i>
                                    {{- else -}}
                                        <i class="fas fa-link"></i>
                                    {{- end -}}
                                </a>
                            {{- end -}}
                        {{- end -}}
                    {{- end -}}
                </div>
            {{- end -}}
            
            {{- /* Contact Button */ -}}
            <div class="about-contact mt-3">
                <a href="{{ "contact" | relLangURL }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-envelope me-1"></i>
                    {{ i18n "contactMe" | default "Contact Me" }}
                </a>
            </div>
        </div>
    </div>
</div>
