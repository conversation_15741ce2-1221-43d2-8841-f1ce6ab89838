{{- /* Sidebar Component */ -}}
<aside class="sidebar">
    <div class="sidebar-content">
        {{- /* About Widget */ -}}
        {{- if .Site.Params.sidebar.about.enable -}}
            {{- partial "sidebar/about.html" . -}}
        {{- end -}}
        
        {{- /* Search Widget (Mobile) */ -}}
        {{- if .Site.Params.header.search.enable -}}
            <div class="sidebar-widget d-lg-none">
                <div class="widget-header">
                    <h4 class="widget-title">
                        <i class="fas fa-search"></i>
                        {{ i18n "search" | default "Search" }}
                    </h4>
                </div>
                <div class="widget-content">
                    <button class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#searchModal">
                        <i class="fas fa-search me-2"></i>
                        {{ i18n "searchArticles" | default "Search Articles" }}
                    </button>
                </div>
            </div>
        {{- end -}}
        
        {{- /* Popular Posts Widget */ -}}
        {{- if .Site.Params.sidebar.popular.enable -}}
            {{- partial "sidebar/popular-posts.html" . -}}
        {{- end -}}
        
        {{- /* Recent Posts Widget */ -}}
        {{- partial "sidebar/recent-posts.html" . -}}
        
        {{- /* Categories Widget */ -}}
        {{- if and .Site.Taxonomies.categories (gt (len .Site.Taxonomies.categories) 0) -}}
            {{- partial "sidebar/categories.html" . -}}
        {{- end -}}

        {{- /* Tags Widget */ -}}
        {{- if and .Site.Taxonomies.tags (gt (len .Site.Taxonomies.tags) 0) -}}
            {{- partial "sidebar/tags.html" . -}}
        {{- end -}}
        
        {{- /* Newsletter Widget */ -}}
        {{- if .Site.Params.sidebar.newsletter.enable -}}
            {{- partial "sidebar/newsletter.html" . -}}
        {{- end -}}
        
        {{- /* Social Media Widget */ -}}
        {{- if .Site.Params.sidebar.social.enable -}}
            {{- partial "sidebar/social.html" . -}}
        {{- end -}}
        
        {{- /* Archive Widget */ -}}
        {{- partial "sidebar/archive.html" . -}}
        
        {{- /* Custom Widgets */ -}}
        {{- partial "sidebar/custom.html" . -}}
    </div>
</aside>
