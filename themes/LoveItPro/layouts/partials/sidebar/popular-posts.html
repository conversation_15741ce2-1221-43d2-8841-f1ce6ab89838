{{- /* Popular Posts Widget */ -}}
{{- $popularCount := .Site.Params.sidebar.popular.count | default 5 -}}
{{- $popularPosts := where .Site.RegularPages "Params.popular" true -}}
{{- if not $popularPosts -}}
    {{- $popularPosts = first $popularCount .Site.RegularPages -}}
{{- end -}}

{{- if $popularPosts -}}
<div class="sidebar-widget popular-posts-widget">
    <div class="widget-header">
        <h4 class="widget-title">
            <i class="fas fa-fire"></i>
            {{ .Site.Params.sidebar.popular.title | default (i18n "popularPosts" | default "Popular Posts") }}
        </h4>
    </div>
    <div class="widget-content">
        <div class="popular-posts-list">
            {{- range $index, $post := $popularPosts -}}
                <article class="popular-post-item">
                    <div class="row g-2">
                        {{- /* Post Image */ -}}
                        {{- if $post.Params.image -}}
                            <div class="col-4">
                                <a href="{{ $post.RelPermalink }}" class="post-image-link">
                                    <img src="{{ $post.Params.image | relURL }}" 
                                         alt="{{ $post.Title }}" 
                                         class="img-fluid rounded"
                                         loading="lazy">
                                </a>
                            </div>
                            <div class="col-8">
                        {{- else -}}
                            <div class="col-12">
                        {{- end -}}
                            <div class="post-content">
                                {{- /* Post Number */ -}}
                                <div class="post-number">{{ add $index 1 }}</div>
                                
                                {{- /* Post Title */ -}}
                                <h6 class="post-title">
                                    <a href="{{ $post.RelPermalink }}" class="text-decoration-none">
                                        {{ $post.Title }}
                                    </a>
                                </h6>
                                
                                {{- /* Post Meta */ -}}
                                <div class="post-meta">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt"></i>
                                        {{ $post.Date.Format (.Site.Params.dateFormat | default "Jan 2") }}
                                        
                                        {{- if $.Site.Params.article.readingTime.enable -}}
                                            <span class="mx-1">•</span>
                                            <i class="fas fa-clock"></i>
                                            {{ partial "components/reading-time.html" $post }}
                                        {{- end -}}
                                    </small>
                                </div>
                                
                                {{- /* Post Category */ -}}
                                {{- with $post.Params.categories -}}
                                    <div class="post-category">
                                        <a href="{{ printf "/categories/%s/" (index . 0 | urlize) | relLangURL }}" 
                                           class="badge bg-primary text-decoration-none">
                                            {{ index . 0 }}
                                        </a>
                                    </div>
                                {{- end -}}
                            </div>
                        </div>
                    </div>
                </article>
                
                {{- if lt (add $index 1) (len $popularPosts) -}}
                    <hr class="post-divider">
                {{- end -}}
            {{- end -}}
        </div>
        
        {{- /* View All Link */ -}}
        <div class="widget-footer text-center mt-3">
            <a href="{{ "posts" | relLangURL }}" class="btn btn-outline-primary btn-sm">
                {{ i18n "viewAllPosts" | default "View All Posts" }}
                <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
    </div>
</div>
{{- end -}}
