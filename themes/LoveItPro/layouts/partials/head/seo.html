{{- /* SEO Meta Tags */ -}}

{{- /* Basic Meta Tags */ -}}
<meta name="robots" content="{{ if .Params.noindex }}noindex, nofollow{{ else }}index, follow{{ end }}">
<meta name="googlebot" content="{{ if .Params.noindex }}noindex, nofollow{{ else }}index, follow{{ end }}">

{{- /* Open Graph Meta Tags */ -}}
<meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}">
<meta property="og:title" content="{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }} - {{ .Site.Title }}{{ end }}">
<meta property="og:description" content="{{ with .Description }}{{ . }}{{ else }}{{ with .Summary }}{{ . }}{{ else }}{{ .Site.Params.description }}{{ end }}{{ end }}">
<meta property="og:url" content="{{ .Permalink }}">
<meta property="og:site_name" content="{{ .Site.Title }}">
<meta property="og:locale" content="{{ .Site.Language.Lang }}">

{{- /* Open Graph Image */ -}}
{{- $ogImage := "" -}}
{{- if .Params.image -}}
    {{- $ogImage = .Params.image | absURL -}}
{{- else if .Site.Params.seo.image -}}
    {{- $ogImage = .Site.Params.seo.image | absURL -}}
{{- end -}}

{{- if $ogImage -}}
    <meta property="og:image" content="{{ $ogImage }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:alt" content="{{ .Title }}">
{{- end -}}

{{- /* Article-specific Open Graph */ -}}
{{- if .IsPage -}}
    <meta property="article:published_time" content="{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}">
    <meta property="article:modified_time" content="{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" }}">
    
    {{- with .Params.author | default .Site.Params.author -}}
        <meta property="article:author" content="{{ . }}">
    {{- end -}}
    
    {{- with .Params.categories -}}
        {{- range . -}}
            <meta property="article:section" content="{{ . }}">
        {{- end -}}
    {{- end -}}
    
    {{- with .Params.tags -}}
        {{- range . -}}
            <meta property="article:tag" content="{{ . }}">
        {{- end -}}
    {{- end -}}
{{- end -}}

{{- /* Twitter Card Meta Tags */ -}}
<meta name="twitter:card" content="{{ .Site.Params.seo.twitterCard | default "summary_large_image" }}">
<meta name="twitter:title" content="{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }}{{ end }}">
<meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{ with .Summary }}{{ . }}{{ else }}{{ .Site.Params.description }}{{ end }}{{ end }}">

{{- if $ogImage -}}
    <meta name="twitter:image" content="{{ $ogImage }}">
    <meta name="twitter:image:alt" content="{{ .Title }}">
{{- end -}}

{{- with .Site.Params.social.Twitter -}}
    <meta name="twitter:site" content="@{{ . }}">
    <meta name="twitter:creator" content="@{{ . }}">
{{- end -}}

{{- /* Additional Meta Tags */ -}}
{{- if .IsPage -}}
    <meta name="article:word_count" content="{{ .WordCount }}">
    <meta name="article:reading_time" content="{{ div (add .WordCount 200) 200 }}">
{{- end -}}

{{- /* Schema.org JSON-LD */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "{{ if .IsPage }}BlogPosting{{ else if eq .Kind "taxonomy" }}CollectionPage{{ else }}WebSite{{ end }}",
    "name": "{{ if .IsHome }}{{ .Site.Title }}{{ else }}{{ .Title }}{{ end }}",
    "headline": "{{ .Title }}",
    "description": "{{ with .Description }}{{ . }}{{ else }}{{ with .Summary }}{{ . }}{{ else }}{{ .Site.Params.description }}{{ end }}{{ end }}",
    "url": "{{ .Permalink }}",
    {{- if .IsPage }}
    "datePublished": "{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}",
    "dateModified": "{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" }}",
    "wordCount": {{ .WordCount }},
    {{- end }}
    "author": {
        "@type": "Person",
        "name": "{{ .Params.author | default .Site.Params.author }}",
        "url": "{{ .Site.BaseURL }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "url": "{{ .Site.BaseURL }}",
        {{- if .Site.Params.seo.image }}
        "logo": {
            "@type": "ImageObject",
            "url": "{{ .Site.Params.seo.image | absURL }}",
            "width": 600,
            "height": 60
        },
        {{- end }}
        "sameAs": [
            {{- $social := slice -}}
            {{- range $name, $url := .Site.Params.social -}}
                {{- if and $url (ne $name "Email") (ne $name "RSS") -}}
                    {{- $social = $social | append (printf "\"%s\"" $url) -}}
                {{- end -}}
            {{- end -}}
            {{ delimit $social ", " | safeHTML }}
        ]
    },
    {{- if $ogImage }}
    "image": {
        "@type": "ImageObject",
        "url": "{{ $ogImage }}",
        "width": 1200,
        "height": 630
    },
    {{- end }}
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "{{ .Permalink }}"
    },
    {{- if .IsPage }}
    "articleSection": "{{ with .Params.categories }}{{ index . 0 }}{{ end }}",
    "keywords": "{{ with .Params.tags }}{{ delimit . ", " }}{{ end }}",
    {{- end }}
    "inLanguage": "{{ .Site.Language.Lang }}",
    "potentialAction": {
        "@type": "ReadAction",
        "target": "{{ .Permalink }}"
    }
}
</script>

{{- /* Breadcrumb Schema */ -}}
{{- if not .IsHome -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "{{ .Site.Title }}",
            "item": "{{ .Site.BaseURL }}"
        }
        {{- if .Section -}},
        {
            "@type": "ListItem",
            "position": 2,
            "name": "{{ .Section | title }}",
            "item": "{{ printf "%s/%s/" .Site.BaseURL .Section }}"
        }
        {{- end -}}
        {{- if .IsPage -}},
        {
            "@type": "ListItem",
            "position": {{ if .Section }}3{{ else }}2{{ end }},
            "name": "{{ .Title }}",
            "item": "{{ .Permalink }}"
        }
        {{- end -}}
    ]
}
</script>
{{- end -}}

{{- /* Website Schema (Homepage only) */ -}}
{{- if .IsHome -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ .Site.Title }}",
    "url": "{{ .Site.BaseURL }}",
    "description": "{{ .Site.Params.description }}",
    "inLanguage": "{{ .Site.Language.Lang }}",
    {{- if .Site.Params.header.search.enable }}
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ .Site.BaseURL }}?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    },
    {{- end }}
    "publisher": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "url": "{{ .Site.BaseURL }}"
    }
}
</script>
{{- end -}}
