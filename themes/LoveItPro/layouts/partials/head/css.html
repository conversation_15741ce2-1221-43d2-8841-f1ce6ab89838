{{- /* CSS Assets with <PERSON> */ -}}

{{- /* Bootstrap CSS */ -}}
{{- if eq .Site.Params.assets.framework "bootstrap" -}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUa6J2lPEAI4xDDuiEnQqpJnb+EFqLU6qdf+sSrhEjmDyDEV4FT4EhiKOx+k" crossorigin="anonymous">
{{- else if eq .Site.Params.assets.framework "tailwind" -}}
    <script src="https://cdn.tailwindcss.com"></script>
{{- end -}}

{{- /* Font Awesome */ -}}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecd<PERSON>l7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

{{- /* Main Theme SCSS */ -}}
{{- $scss := resources.Get "css/main.scss" -}}
{{- if $scss -}}
    {{- $css := $scss | resources.ToCSS (dict "targetPath" "css/style.css" "enableSourceMap" (not hugo.IsProduction)) -}}
    {{- if hugo.IsProduction -}}
        {{- $css = $css | resources.Minify | resources.Fingerprint "sha256" -}}
        <link rel="stylesheet" href="{{ $css.RelPermalink }}" integrity="{{ $css.Data.Integrity }}" crossorigin="anonymous">
    {{- else -}}
        <link rel="stylesheet" href="{{ $css.RelPermalink }}">
    {{- end -}}
{{- end -}}

{{- /* Syntax Highlighting CSS */ -}}
{{- if .Site.Params.markup.highlight.style -}}
    {{- $highlightCSS := resources.Get (printf "css/syntax/%s.css" .Site.Params.markup.highlight.style) -}}
    {{- if $highlightCSS -}}
        {{- if hugo.IsProduction -}}
            {{- $highlightCSS = $highlightCSS | resources.Minify | resources.Fingerprint "sha256" -}}
            <link rel="stylesheet" href="{{ $highlightCSS.RelPermalink }}" integrity="{{ $highlightCSS.Data.Integrity }}" crossorigin="anonymous">
        {{- else -}}
            <link rel="stylesheet" href="{{ $highlightCSS.RelPermalink }}">
        {{- end -}}
    {{- end -}}
{{- end -}}

{{- /* Custom CSS Files */ -}}
{{- range .Site.Params.assets.customCSS -}}
    {{- if hasPrefix . "http" -}}
        <link rel="stylesheet" href="{{ . }}" crossorigin="anonymous">
    {{- else -}}
        {{- $customCSS := resources.Get . -}}
        {{- if $customCSS -}}
            {{- if hugo.IsProduction -}}
                {{- $customCSS = $customCSS | resources.Minify | resources.Fingerprint "sha256" -}}
                <link rel="stylesheet" href="{{ $customCSS.RelPermalink }}" integrity="{{ $customCSS.Data.Integrity }}" crossorigin="anonymous">
            {{- else -}}
                <link rel="stylesheet" href="{{ $customCSS.RelPermalink }}">
            {{- end -}}
        {{- end -}}
    {{- end -}}
{{- end -}}

{{- /* Theme Variables */ -}}
<style>
:root {
    --primary-color: {{ .Site.Params.colors.primary | default "#007bff" }};
    --secondary-color: {{ .Site.Params.colors.secondary | default "#6c757d" }};
    --success-color: {{ .Site.Params.colors.success | default "#28a745" }};
    --danger-color: {{ .Site.Params.colors.danger | default "#dc3545" }};
    --warning-color: {{ .Site.Params.colors.warning | default "#ffc107" }};
    --info-color: {{ .Site.Params.colors.info | default "#17a2b8" }};
    --light-color: {{ .Site.Params.colors.light | default "#f8f9fa" }};
    --dark-color: {{ .Site.Params.colors.dark | default "#343a40" }};
    
    --font-family-sans-serif: {{ .Site.Params.fonts.sansSerif | default "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif" }};
    --font-family-serif: {{ .Site.Params.fonts.serif | default "Georgia, 'Times New Roman', Times, serif" }};
    --font-family-monospace: {{ .Site.Params.fonts.monospace | default "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace" }};
    
    --font-size-base: {{ .Site.Params.fonts.size.base | default "1rem" }};
    --font-size-lg: {{ .Site.Params.fonts.size.lg | default "1.25rem" }};
    --font-size-sm: {{ .Site.Params.fonts.size.sm | default "0.875rem" }};
    
    --border-radius: {{ .Site.Params.design.borderRadius | default "0.375rem" }};
    --box-shadow: {{ .Site.Params.design.boxShadow | default "0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)" }};
    --transition: {{ .Site.Params.design.transition | default "all 0.15s ease-in-out" }};
}

{{- /* Dark Mode Variables */ -}}
[data-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #e9ecef;
    --bs-card-bg: #2d2d2d;
    --bs-border-color: #404040;
}
</style>
