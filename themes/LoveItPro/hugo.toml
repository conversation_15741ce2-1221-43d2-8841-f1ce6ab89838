# LoveItPro Theme Configuration
# Enhanced Hugo theme with modern features and responsive design

[module]
  [module.hugoVersion]
    min = "0.128.0"

# Site Configuration
baseURL = "https://example.org/"
title = "LoveItPro Blog"
defaultContentLanguage = "zh-cn"
languageCode = "zh-CN"
languageName = "简体中文"
hasCJKLanguage = true

# Hugo Configuration
enableRobotsTXT = true
enableGitInfo = true
enableEmoji = true
paginate = 12
rssLimit = 20

# Menu Configuration
[menu]
  [[menu.main]]
    weight = 1
    identifier = "posts"
    name = "所有文章"
    url = "/posts/"
    title = "所有文章"
  [[menu.main]]
    weight = 2
    identifier = "categories"
    name = "分类"
    url = "/categories/"
    title = "分类"
  [[menu.main]]
    weight = 3
    identifier = "tags"
    name = "标签"
    url = "/tags/"
    title = "标签"
  [[menu.main]]
    weight = 4
    identifier = "about"
    name = "关于"
    url = "/about/"
    title = "关于"
  [[menu.main]]
    weight = 5
    identifier = "contact"
    name = "联系"
    url = "/contact/"
    title = "联系"

# Theme Parameters
[params]
  # Theme Configuration
  version = "1.0.0"
  description = "A clean, elegant but advanced Hugo theme"
  keywords = ["blog", "hugo", "theme", "responsive"]
  defaultTheme = "auto" # auto, light, dark
  
  # Site Information
  author = "WenHao"
  email = "<EMAIL>"
  avatar = "/images/avatar.png"
  subtitle = "文浩的个人博客"
  
  # SEO Configuration
  [params.seo]
    enable = true
    image = "/images/logo.png"
    twitterCard = "summary_large_image"
    
  # Header Configuration
  [params.header]
    enable = true
    logo = "/images/logo.png"
    title = "LoveItPro"
    subtitle = "Modern Hugo Theme"
    
    # Carousel Configuration
    [params.header.carousel]
      enable = true
      autoplay = true
      interval = 5000
      showIndicators = true
      showControls = true
      
    # Search Configuration
    [params.header.search]
      enable = true
      placeholder = "搜索文章..."
      maxResults = 10
      
  # Home Page Configuration
  [params.home]
    # Featured Posts
    [params.home.featured]
      enable = true
      title = "精选文章"
      count = 3
      
    # Recent Posts
    [params.home.posts]
      enable = true
      title = "最新文章"
      count = 6
      paginate = 6
      
  # Article Configuration
  [params.article]
    # Card Layout
    [params.article.card]
      enable = true
      showImage = true
      showExcerpt = true
      showTags = true
      showReadingTime = true
      showDate = true
      showAuthor = true
      
    # Reading Time
    [params.article.readingTime]
      enable = true
      wordsPerMinute = 200
      
  # Sidebar Configuration
  [params.sidebar]
    enable = true
    position = "right" # left, right
    
    # About Widget
    [params.sidebar.about]
      enable = true
      title = "关于博主"
      avatar = "/images/avatar.png"
      description = "欢迎来到我的博客"
      
    # Popular Posts Widget
    [params.sidebar.popular]
      enable = true
      title = "热门文章"
      count = 5
      
    # Newsletter Widget
    [params.sidebar.newsletter]
      enable = true
      title = "订阅博客"
      description = "获取最新文章推送"
      action = "/newsletter/subscribe"
      
    # Social Media Widget
    [params.sidebar.social]
      enable = true
      title = "关注我"
      
  # Social Media Links
  [params.social]
    GitHub = "wenhaofree"
    Twitter = "wenhaofree"
    Email = "<EMAIL>"
    RSS = true
    
  # Comment System
  [params.comments]
    enable = true
    provider = "disqus" # disqus, gitalk, valine
    [params.comments.disqus]
      shortname = "your-disqus-shortname"
      
  # Analytics
  [params.analytics]
    [params.analytics.google]
      id = ""
      
  # Asset Configuration
  [params.assets]
    # CSS Framework
    framework = "bootstrap" # bootstrap, tailwind
    # Custom CSS
    customCSS = []
    # Custom JS
    customJS = []
