version: 2.1

executors:
  hugo:
    parameters:
      version:
        description: "version tag"
        type: string
    docker:
      - image: cibuilds/hugo:<<parameters.version>>

jobs:
  build-check:
    parameters:
      version:
        description: "version tag"
        type: string
      environment:
        description: "hugo environment"
        type: string
    executor:
      name: hugo
      version: <<parameters.version>>
    working_directory: ~/LoveIt
    steps:
      - checkout
      - run: git submodule sync
      - run: git submodule update --init
      - run: hugo --source exampleSite --gc --minify --environment <<parameters.environment>>
      - run: cp -rf exampleSite/resources . && git diff --exit-code
      - run: htmlproofer exampleSite/public --disable-external true --ignore-missing-alt true

workflows:
  build-check:
    jobs:
      - build-check:
          matrix:
            parameters:
              version: [ "0.128.0", "0.145.0" ]
              environment: [ development, production ]
