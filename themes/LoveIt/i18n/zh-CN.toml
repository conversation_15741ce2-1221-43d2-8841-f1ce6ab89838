# Translations for Simplified Chinese
# 简体中文的翻译
# https://gohugo.io/content-management/multilingual/#translation-of-strings

# === baseof ==
[backToTop]
other = "回到顶部"

[viewComments]
other = "查看评论"
# === baseof ==

# === Post ===
[posts]
other = "文章"

[allArticles]
other = "所有文章"

[about]
other = "关于"
# === Post ===

# === Taxonomy ===
[allSome]
other = "所有{{ .Some }}"

[tag]
other = "标签"

[tags]
other = "标签"

[category]
other = "分类"

[categories]
other = "分类"
# === Taxonomy ===

# === Pagination ===
[more]
other = "更多"
# === Pagination ===

# === partials/header.html ===
[selectLanguage]
other = "选择语言"

[switchTheme]
other = "切换主题"
# === partials/header.html ===

# === partials/footer.html ===
[poweredBySome]
other = "由 {{ .Hugo }} 强力驱动 | 主题 - {{ .Theme }}"
# === partials/footer.html ===

# === partials/comment.html ===
[valineLang]
other = "zh-CN"

[valinePlaceholder]
other = "你的评论 ..."

[facebookLanguageCode]
other = "zh_CN"

[giscusLang]
other = "zh-CN"

[walineLang]
other = "zh-CN"
# === partials/comment.html ===

# === partials/assets.html ===
[search]
other = "搜索"

[searchPlaceholder]
other = "搜索文章标题或内容..."

[clear]
other = "清空"

[cancel]
other = "取消"

[noResultsFound]
other = "没有找到结果"

[lunrLanguageCode]
other = "zh"

[lunrLanguageLib]
other = "lib/lunr/lunr.zh.js"

[lunrSegmentitLib]
other = "lib/lunr/lunr.segmentit.js"

[copyToClipboard]
other = "复制到剪贴板"

[cookieconsentMessage]
other = "本网站使用 Cookies 来改善您的浏览体验."

[cookieconsentDismiss]
other = "同意"

[cookieconsentLink]
other = "了解更多"
# === partials/assets.html ===

# === partials/plugin/share.html ===
[shareOn]
other = "分享到"
# === partials/plugin/share.html ===

# === posts/single.html ===
[contents]
other = "目录"

[publishedOnDate]
other = "发布于 {{ .Date }}"

[includedInCategories]
other = "收录于 {{ .Categories }}"

[wordCount]
other = "约 {{ .Count }} 字"

[readingTime]
other = "预计阅读 {{ .Count }} 分钟"

[views]
other = "次阅读"

[author]
other = "作者"

[updatedOnDate]
other = "更新于 {{ .Date }}"

[readMarkdown]
other = "阅读原始文档"

[back]
other = "返回"

[home]
other = "主页"

[readMore]
other = "阅读全文"
# === posts/single.html ===

# === 404.html ===
[pageNotFound]
other = "页面没找到"

[pageNotFoundText]
other = "抱歉，您要查找的页面不存在。"
# === 404.html ===

# === shortcodes/admonition.html ===
[note]
other = "注意"

[abstract]
other = "摘要"

[info]
other = "信息"

[tip]
other = "技巧"

[success]
other = "成功"

[question]
other = "问题"

[warning]
other = "警告"

[failure]
other = "失败"

[danger]
other = "危险"

[bug]
other = "Bug"

[example]
other = "示例"

[quote]
other = "引用"
# === shortcodes/admonition.html ===

# === shortcodes/version.html ===
[new]
other = "新增"

[changed]
other = "更改"

[deleted]
other = "删除"

[deprecated]
other = "弃用"
# === shortcodes/version.html ===
