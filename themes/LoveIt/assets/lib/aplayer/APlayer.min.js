!function(e,a){"object"==typeof exports&&"object"==typeof module?module.exports=a():"function"==typeof define&&define.amd?define("APlayer",[],a):"object"==typeof exports?exports.APlayer=a():e.APlayer=a()}(window,(function(){return function(e){var a={};function t(r){if(a[r])return a[r].exports;var i=a[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=a,t.d=function(e,a,r){t.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,a){if(1&a&&(e=t(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var i in e)t.d(r,i,function(a){return e[a]}.bind(null,i));return r},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},t.p="/",t(t.s=31)}([function(e,a,t){var r=t(2);e.exports=function(e){"use strict";e=e||{};var a="",t=r.$each,i=e.audio,n=(e.$value,e.$index,r.$escape),o=e.theme,l=e.index;return t(i,(function(e,t){a+='\n<li>\n    <span class="aplayer-list-cur" style="background-color: ',a+=n(e.theme||o),a+=';"></span>\n    <span class="aplayer-list-index">',a+=n(t+l),a+='</span>\n    <span class="aplayer-list-title">',a+=n(e.name),a+='</span>\n    <span class="aplayer-list-author">',a+=n(e.artist),a+="</span>\n</li>\n"})),a}},function(e,a){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"===("undefined"==typeof window?"undefined":t(window))&&(r=window)}e.exports=r},function(e,a,t){"use strict";e.exports=t(30)},function(e,a,t){"use strict";var r=t(6),i=t.n(r),n=t(7),o=t.n(n)()(i.a);o.push([e.i,'.aplayer{background:#fff;font-family:Arial,Helvetica,sans-serif;margin:5px;box-shadow:0 2px 2px 0 rgba(0,0,0,.07),0 1px 5px 0 rgba(0,0,0,.1);border-radius:2px;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;line-height:normal;position:relative}.aplayer *{box-sizing:content-box}.aplayer svg{width:100%;height:100%}.aplayer svg circle,.aplayer svg path{fill:#fff}.aplayer.aplayer-withlist .aplayer-info{border-bottom:1px solid #e9e9e9}.aplayer.aplayer-withlist .aplayer-list{display:block}.aplayer.aplayer-withlist .aplayer-icon-order,.aplayer.aplayer-withlist .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-menu{display:inline}.aplayer.aplayer-withlrc .aplayer-pic{height:90px;width:90px}.aplayer.aplayer-withlrc .aplayer-info{margin-left:90px;height:90px;padding:10px 7px 0}.aplayer.aplayer-withlrc .aplayer-lrc{display:block}.aplayer.aplayer-narrow{width:66px}.aplayer.aplayer-narrow .aplayer-info,.aplayer.aplayer-narrow .aplayer-list{display:none}.aplayer.aplayer-narrow .aplayer-body,.aplayer.aplayer-narrow .aplayer-pic{height:66px;width:66px}.aplayer.aplayer-fixed{position:fixed;bottom:0;left:0;right:0;margin:0;z-index:99;overflow:visible;max-width:400px;box-shadow:none}.aplayer.aplayer-fixed .aplayer-list{margin-bottom:65px;border:1px solid #eee;border-bottom:none}.aplayer.aplayer-fixed .aplayer-body{position:fixed;bottom:0;left:0;right:0;margin:0;z-index:99;background:#fff;padding-right:18px;transition:all .3s ease;max-width:400px}.aplayer.aplayer-fixed .aplayer-lrc{display:block;position:fixed;bottom:10px;left:0;right:0;margin:0;z-index:98;pointer-events:none;text-shadow:-1px -1px 0 #fff}.aplayer.aplayer-fixed .aplayer-lrc:after,.aplayer.aplayer-fixed .aplayer-lrc:before{display:none}.aplayer.aplayer-fixed .aplayer-info{transform:scaleX(1);transform-origin:0 0;transition:all .3s ease;border-bottom:none;border-top:1px solid #e9e9e9}.aplayer.aplayer-fixed .aplayer-info .aplayer-music{width:calc(100% - 105px)}.aplayer.aplayer-fixed .aplayer-miniswitcher{display:block}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-info{display:block;transform:scaleX(0)}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-body{width:66px!important}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-miniswitcher .aplayer-icon{transform:rotateY(0)}.aplayer.aplayer-fixed .aplayer-icon-back,.aplayer.aplayer-fixed .aplayer-icon-forward,.aplayer.aplayer-fixed .aplayer-icon-lrc,.aplayer.aplayer-fixed .aplayer-icon-play{display:inline-block}.aplayer.aplayer-fixed .aplayer-icon-back,.aplayer.aplayer-fixed .aplayer-icon-forward,.aplayer.aplayer-fixed .aplayer-icon-menu,.aplayer.aplayer-fixed .aplayer-icon-play{position:absolute;bottom:27px;width:20px;height:20px}.aplayer.aplayer-fixed .aplayer-icon-back{right:75px}.aplayer.aplayer-fixed .aplayer-icon-play{right:50px}.aplayer.aplayer-fixed .aplayer-icon-forward{right:25px}.aplayer.aplayer-fixed .aplayer-icon-menu{right:0}.aplayer.aplayer-arrow .aplayer-icon-loop,.aplayer.aplayer-arrow .aplayer-icon-order,.aplayer.aplayer-mobile .aplayer-icon-volume-down{display:none}.aplayer.aplayer-loading .aplayer-info .aplayer-controller .aplayer-loading-icon{display:block}.aplayer.aplayer-loading .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb{transform:scale(1)}.aplayer .aplayer-body{position:relative}.aplayer .aplayer-icon{width:15px;height:15px;border:none;background-color:transparent;outline:none;cursor:pointer;opacity:.8;vertical-align:middle;padding:0;font-size:12px;margin:0;display:inline-block}.aplayer .aplayer-icon path{transition:all .2s ease-in-out}.aplayer .aplayer-icon-back,.aplayer .aplayer-icon-forward,.aplayer .aplayer-icon-lrc,.aplayer .aplayer-icon-order,.aplayer .aplayer-icon-play{display:none}.aplayer .aplayer-icon-lrc-inactivity svg{opacity:.4}.aplayer .aplayer-icon-forward{transform:rotate(180deg)}.aplayer .aplayer-lrc-content{display:none}.aplayer .aplayer-pic{position:relative;float:left;height:66px;width:66px;background-size:cover;background-position:50%;transition:all .3s ease;cursor:pointer}.aplayer .aplayer-pic:hover .aplayer-button{opacity:1}.aplayer .aplayer-pic .aplayer-button{position:absolute;border-radius:50%;opacity:.8;text-shadow:0 1px 1px rgba(0,0,0,.2);box-shadow:0 1px 1px rgba(0,0,0,.2);background:rgba(0,0,0,.2);transition:all .1s ease}.aplayer .aplayer-pic .aplayer-button path{fill:#fff}.aplayer .aplayer-pic .aplayer-hide{display:none}.aplayer .aplayer-pic .aplayer-play{width:26px;height:26px;border:2px solid #fff;bottom:50%;right:50%;margin:0 -15px -15px 0}.aplayer .aplayer-pic .aplayer-play svg{position:absolute;top:3px;left:4px;height:20px;width:20px}.aplayer .aplayer-pic .aplayer-pause{width:16px;height:16px;border:2px solid #fff;bottom:4px;right:4px}.aplayer .aplayer-pic .aplayer-pause svg{position:absolute;top:2px;left:2px;height:12px;width:12px}.aplayer .aplayer-info{margin-left:66px;padding:14px 7px 0 10px;height:66px;box-sizing:border-box}.aplayer .aplayer-info .aplayer-music{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin:0 0 13px 5px;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;cursor:default;padding-bottom:2px;height:20px}.aplayer .aplayer-info .aplayer-music .aplayer-title{font-size:14px}.aplayer .aplayer-info .aplayer-music .aplayer-author{font-size:12px;color:#666}.aplayer .aplayer-info .aplayer-controller{position:relative;display:flex}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap{margin:0 0 0 5px;padding:4px 0;cursor:pointer!important;flex:1}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap:hover .aplayer-bar .aplayer-played .aplayer-thumb{transform:scale(1)}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar{position:relative;height:2px;width:100%;background:#cdcdcd}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded{position:absolute;left:0;top:0;bottom:0;background:#aaa;height:2px;transition:all .5s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played{position:absolute;left:0;top:0;bottom:0;height:2px}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb{position:absolute;top:0;right:5px;margin-top:-4px;margin-right:-10px;height:10px;width:10px;border-radius:50%;cursor:pointer;transition:all .3s ease-in-out;transform:scale(0)}.aplayer .aplayer-info .aplayer-controller .aplayer-time{position:relative;right:0;bottom:4px;height:17px;color:#999;font-size:11px;padding-left:7px}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-time-inner{vertical-align:middle}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon{cursor:pointer;transition:all .2s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon path{fill:#666}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-loop{margin-right:2px}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon:hover path{fill:#000}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-menu,.aplayer .aplayer-info .aplayer-controller .aplayer-time.aplayer-time-narrow .aplayer-icon-menu,.aplayer .aplayer-info .aplayer-controller .aplayer-time.aplayer-time-narrow .aplayer-icon-mode{display:none}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap{position:relative;display:inline-block;margin-left:3px;cursor:pointer!important}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap:hover .aplayer-volume-bar-wrap{height:40px}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap{position:absolute;bottom:15px;right:-3px;width:25px;height:0;z-index:99;overflow:hidden;transition:all .2s ease-in-out}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap.aplayer-volume-bar-wrap-active{height:40px}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar{position:absolute;bottom:0;right:10px;width:5px;height:35px;background:#aaa;border-radius:2.5px;overflow:hidden}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar .aplayer-volume{position:absolute;bottom:0;right:0;width:5px;transition:all .1s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-loading-icon{display:none}.aplayer .aplayer-info .aplayer-controller .aplayer-loading-icon svg{position:absolute;-webkit-animation:rotate 1s linear infinite;animation:rotate 1s linear infinite}.aplayer .aplayer-lrc{display:none;position:relative;height:30px;text-align:center;overflow:hidden;margin:-10px 0 7px}.aplayer .aplayer-lrc:before{top:0;height:10%;background:linear-gradient(180deg,#fff 0,hsla(0,0%,100%,0));filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#00ffffff",GradientType=0)}.aplayer .aplayer-lrc:after,.aplayer .aplayer-lrc:before{position:absolute;z-index:1;display:block;overflow:hidden;width:100%;content:" "}.aplayer .aplayer-lrc:after{bottom:0;height:33%;background:linear-gradient(180deg,hsla(0,0%,100%,0) 0,hsla(0,0%,100%,.8));filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#00ffffff",endColorstr="#ccffffff",GradientType=0)}.aplayer .aplayer-lrc p{font-size:12px;color:#666;line-height:16px!important;height:16px!important;padding:0!important;margin:0!important;transition:all .5s ease-out;opacity:.4;overflow:hidden}.aplayer .aplayer-lrc p.aplayer-lrc-current{opacity:1;overflow:visible;height:auto!important;min-height:16px}.aplayer .aplayer-lrc.aplayer-lrc-hide{display:none}.aplayer .aplayer-lrc .aplayer-lrc-contents{width:100%;transition:all .5s ease-out;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;cursor:default}.aplayer .aplayer-list{overflow:auto;transition:all .5s ease;will-change:height;display:none;overflow:hidden;list-style-type:none;margin:0;padding:0;overflow-y:auto}.aplayer .aplayer-list::-webkit-scrollbar{width:5px}.aplayer .aplayer-list::-webkit-scrollbar-thumb{border-radius:3px;background-color:#eee}.aplayer .aplayer-list::-webkit-scrollbar-thumb:hover{background-color:#ccc}.aplayer .aplayer-list li{position:relative;height:32px;line-height:32px;padding:0 15px;font-size:12px;border-top:1px solid #e9e9e9;cursor:pointer;transition:all .2s ease;overflow:hidden;margin:0}.aplayer .aplayer-list li:first-child{border-top:none}.aplayer .aplayer-list li:hover{background:#efefef}.aplayer .aplayer-list li.aplayer-list-light{background:#e9e9e9}.aplayer .aplayer-list li.aplayer-list-light .aplayer-list-cur{display:inline-block}.aplayer .aplayer-list li .aplayer-list-cur{display:none;width:3px;height:22px;position:absolute;left:0;top:5px;cursor:pointer}.aplayer .aplayer-list li .aplayer-list-index{color:#666;margin-right:12px;cursor:pointer}.aplayer .aplayer-list li .aplayer-list-author{color:#666;float:right;cursor:pointer}.aplayer .aplayer-notice{opacity:0;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:12px;border-radius:4px;padding:5px 10px;transition:all .3s ease-in-out;overflow:hidden;color:#fff;pointer-events:none;background-color:#f4f4f5;color:#909399}.aplayer .aplayer-miniswitcher{display:none;position:absolute;top:0;right:0;bottom:0;height:100%;background:#e6e6e6;width:18px;border-radius:0 2px 2px 0}.aplayer .aplayer-miniswitcher .aplayer-icon{height:100%;width:100%;transform:rotateY(180deg);transition:all .3s ease}.aplayer .aplayer-miniswitcher .aplayer-icon path{fill:#666}.aplayer .aplayer-miniswitcher .aplayer-icon:hover path{fill:#000}@-webkit-keyframes aplayer-roll{0%{left:0}to{left:-100%}}@keyframes aplayer-roll{0%{left:0}to{left:-100%}}@-webkit-keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(1turn)}}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(1turn)}}',"",{version:3,sources:["webpack://./src/css/index.scss"],names:[],mappings:"AAAA,SAAS,eAAe,CAAC,sCAAsC,CAAC,UAAU,CAAC,iEAAiE,CAAC,iBAAiB,CAAC,eAAe,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,WAAW,sBAAsB,CAAC,aAAa,UAAU,CAAC,WAAW,CAAC,sCAAsC,SAAS,CAAC,wCAAwC,+BAA+B,CAAC,wCAAwC,aAAa,CAAC,wJAAwJ,cAAc,CAAC,sCAAsC,WAAW,CAAC,UAAU,CAAC,uCAAuC,gBAAgB,CAAC,WAAW,CAAC,kBAAkB,CAAC,sCAAsC,aAAa,CAAC,wBAAwB,UAAU,CAAC,4EAA4E,YAAY,CAAC,2EAA2E,WAAW,CAAC,UAAU,CAAC,uBAAuB,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,eAAe,CAAC,eAAe,CAAC,qCAAqC,kBAAkB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,qCAAqC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,eAAe,CAAC,oCAAoC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,qFAAqF,YAAY,CAAC,qCAAqC,mBAAmB,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,oDAAoD,wBAAwB,CAAC,6CAA6C,aAAa,CAAC,oDAAoD,aAAa,CAAC,mBAAmB,CAAC,oDAAoD,oBAAoB,CAAC,0EAA0E,oBAAoB,CAAC,0KAA0K,oBAAoB,CAAC,2KAA2K,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,0CAA0C,UAAU,CAAC,0CAA0C,UAAU,CAAC,6CAA6C,UAAU,CAAC,0CAA0C,OAAO,CAAC,uIAAuI,YAAY,CAAC,iFAAiF,aAAa,CAAC,yHAAyH,kBAAkB,CAAC,uBAAuB,iBAAiB,CAAC,uBAAuB,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,4BAA4B,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,oBAAoB,CAAC,4BAA4B,8BAA8B,CAAC,+IAA+I,YAAY,CAAC,0CAA0C,UAAU,CAAC,+BAA+B,wBAAwB,CAAC,8BAA8B,YAAY,CAAC,sBAAsB,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,cAAc,CAAC,4CAA4C,SAAS,CAAC,sCAAsC,iBAAiB,CAAC,iBAAiB,CAAC,UAAU,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,2CAA2C,SAAS,CAAC,oCAAoC,YAAY,CAAC,oCAAoC,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,sBAAsB,CAAC,wCAAwC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,qCAAqC,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,yCAAyC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,uBAAuB,gBAAgB,CAAC,uBAAuB,CAAC,WAAW,CAAC,qBAAqB,CAAC,sCAAsC,eAAe,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC,qDAAqD,cAAc,CAAC,sDAAsD,cAAc,CAAC,UAAU,CAAC,2CAA2C,iBAAiB,CAAC,YAAY,CAAC,6DAA6D,gBAAgB,CAAC,aAAa,CAAC,wBAAwB,CAAC,MAAM,CAAC,+GAA+G,kBAAkB,CAAC,0EAA0E,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,kBAAkB,CAAC,0FAA0F,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,uBAAuB,CAAC,0FAA0F,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,yGAAyG,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,kBAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,8BAA8B,CAAC,kBAAkB,CAAC,yDAAyD,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,6EAA6E,qBAAqB,CAAC,uEAAuE,cAAc,CAAC,uBAAuB,CAAC,4EAA4E,SAAS,CAAC,yFAAyF,gBAAgB,CAAC,kFAAkF,SAAS,CAAC,yRAAyR,YAAY,CAAC,gEAAgE,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC,wBAAwB,CAAC,+FAA+F,WAAW,CAAC,yFAAyF,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,8BAA8B,CAAC,wHAAwH,WAAW,CAAC,6GAA6G,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,mBAAmB,CAAC,eAAe,CAAC,6HAA6H,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,uBAAuB,CAAC,iEAAiE,YAAY,CAAC,qEAAqE,iBAAiB,CAAC,2CAA2C,CAAC,mCAAmC,CAAC,sBAAsB,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,iBAAiB,CAAC,eAAe,CAAC,kBAAkB,CAAC,6BAA6B,KAAK,CAAC,UAAU,CAAC,2DAA2D,CAAC,iHAAiH,CAAC,yDAAyD,iBAAiB,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,4BAA4B,QAAQ,CAAC,UAAU,CAAC,yEAAyE,CAAC,mHAAmH,CAAC,wBAAwB,cAAc,CAAC,UAAU,CAAC,0BAA0B,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,2BAA2B,CAAC,UAAU,CAAC,eAAe,CAAC,4CAA4C,SAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,eAAe,CAAC,uCAAuC,YAAY,CAAC,4CAA4C,UAAU,CAAC,2BAA2B,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC,uBAAuB,aAAa,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,YAAY,CAAC,eAAe,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,0CAA0C,SAAS,CAAC,gDAAgD,iBAAiB,CAAC,qBAAqB,CAAC,sDAAsD,qBAAqB,CAAC,0BAA0B,iBAAiB,CAAC,WAAW,CAAC,gBAAgB,CAAC,cAAc,CAAC,cAAc,CAAC,4BAA4B,CAAC,cAAc,CAAC,uBAAuB,CAAC,eAAe,CAAC,QAAQ,CAAC,sCAAsC,eAAe,CAAC,gCAAgC,kBAAkB,CAAC,6CAA6C,kBAAkB,CAAC,+DAA+D,oBAAoB,CAAC,4CAA4C,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,8CAA8C,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,+CAA+C,UAAU,CAAC,WAAW,CAAC,cAAc,CAAC,yBAAyB,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,8BAA8B,CAAC,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,eAAe,CAAC,UAAU,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,aAAa,CAAC,+BAA+B,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,yBAAyB,CAAC,6CAA6C,WAAW,CAAC,UAAU,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,kDAAkD,SAAS,CAAC,wDAAwD,SAAS,CAAC,gCAAgC,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,wBAAwB,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,0BAA0B,GAAG,mBAAmB,CAAC,GAAG,uBAAuB,CAAC,CAAC,kBAAkB,GAAG,mBAAmB,CAAC,GAAG,uBAAuB,CAAC",sourcesContent:['.aplayer{background:#fff;font-family:Arial,Helvetica,sans-serif;margin:5px;box-shadow:0 2px 2px 0 rgba(0,0,0,.07),0 1px 5px 0 rgba(0,0,0,.1);border-radius:2px;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;line-height:normal;position:relative}.aplayer *{box-sizing:content-box}.aplayer svg{width:100%;height:100%}.aplayer svg circle,.aplayer svg path{fill:#fff}.aplayer.aplayer-withlist .aplayer-info{border-bottom:1px solid #e9e9e9}.aplayer.aplayer-withlist .aplayer-list{display:block}.aplayer.aplayer-withlist .aplayer-icon-order,.aplayer.aplayer-withlist .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-menu{display:inline}.aplayer.aplayer-withlrc .aplayer-pic{height:90px;width:90px}.aplayer.aplayer-withlrc .aplayer-info{margin-left:90px;height:90px;padding:10px 7px 0}.aplayer.aplayer-withlrc .aplayer-lrc{display:block}.aplayer.aplayer-narrow{width:66px}.aplayer.aplayer-narrow .aplayer-info,.aplayer.aplayer-narrow .aplayer-list{display:none}.aplayer.aplayer-narrow .aplayer-body,.aplayer.aplayer-narrow .aplayer-pic{height:66px;width:66px}.aplayer.aplayer-fixed{position:fixed;bottom:0;left:0;right:0;margin:0;z-index:99;overflow:visible;max-width:400px;box-shadow:none}.aplayer.aplayer-fixed .aplayer-list{margin-bottom:65px;border:1px solid #eee;border-bottom:none}.aplayer.aplayer-fixed .aplayer-body{position:fixed;bottom:0;left:0;right:0;margin:0;z-index:99;background:#fff;padding-right:18px;transition:all .3s ease;max-width:400px}.aplayer.aplayer-fixed .aplayer-lrc{display:block;position:fixed;bottom:10px;left:0;right:0;margin:0;z-index:98;pointer-events:none;text-shadow:-1px -1px 0 #fff}.aplayer.aplayer-fixed .aplayer-lrc:after,.aplayer.aplayer-fixed .aplayer-lrc:before{display:none}.aplayer.aplayer-fixed .aplayer-info{transform:scaleX(1);transform-origin:0 0;transition:all .3s ease;border-bottom:none;border-top:1px solid #e9e9e9}.aplayer.aplayer-fixed .aplayer-info .aplayer-music{width:calc(100% - 105px)}.aplayer.aplayer-fixed .aplayer-miniswitcher{display:block}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-info{display:block;transform:scaleX(0)}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-body{width:66px!important}.aplayer.aplayer-fixed.aplayer-narrow .aplayer-miniswitcher .aplayer-icon{transform:rotateY(0)}.aplayer.aplayer-fixed .aplayer-icon-back,.aplayer.aplayer-fixed .aplayer-icon-forward,.aplayer.aplayer-fixed .aplayer-icon-lrc,.aplayer.aplayer-fixed .aplayer-icon-play{display:inline-block}.aplayer.aplayer-fixed .aplayer-icon-back,.aplayer.aplayer-fixed .aplayer-icon-forward,.aplayer.aplayer-fixed .aplayer-icon-menu,.aplayer.aplayer-fixed .aplayer-icon-play{position:absolute;bottom:27px;width:20px;height:20px}.aplayer.aplayer-fixed .aplayer-icon-back{right:75px}.aplayer.aplayer-fixed .aplayer-icon-play{right:50px}.aplayer.aplayer-fixed .aplayer-icon-forward{right:25px}.aplayer.aplayer-fixed .aplayer-icon-menu{right:0}.aplayer.aplayer-arrow .aplayer-icon-loop,.aplayer.aplayer-arrow .aplayer-icon-order,.aplayer.aplayer-mobile .aplayer-icon-volume-down{display:none}.aplayer.aplayer-loading .aplayer-info .aplayer-controller .aplayer-loading-icon{display:block}.aplayer.aplayer-loading .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb{transform:scale(1)}.aplayer .aplayer-body{position:relative}.aplayer .aplayer-icon{width:15px;height:15px;border:none;background-color:transparent;outline:none;cursor:pointer;opacity:.8;vertical-align:middle;padding:0;font-size:12px;margin:0;display:inline-block}.aplayer .aplayer-icon path{transition:all .2s ease-in-out}.aplayer .aplayer-icon-back,.aplayer .aplayer-icon-forward,.aplayer .aplayer-icon-lrc,.aplayer .aplayer-icon-order,.aplayer .aplayer-icon-play{display:none}.aplayer .aplayer-icon-lrc-inactivity svg{opacity:.4}.aplayer .aplayer-icon-forward{transform:rotate(180deg)}.aplayer .aplayer-lrc-content{display:none}.aplayer .aplayer-pic{position:relative;float:left;height:66px;width:66px;background-size:cover;background-position:50%;transition:all .3s ease;cursor:pointer}.aplayer .aplayer-pic:hover .aplayer-button{opacity:1}.aplayer .aplayer-pic .aplayer-button{position:absolute;border-radius:50%;opacity:.8;text-shadow:0 1px 1px rgba(0,0,0,.2);box-shadow:0 1px 1px rgba(0,0,0,.2);background:rgba(0,0,0,.2);transition:all .1s ease}.aplayer .aplayer-pic .aplayer-button path{fill:#fff}.aplayer .aplayer-pic .aplayer-hide{display:none}.aplayer .aplayer-pic .aplayer-play{width:26px;height:26px;border:2px solid #fff;bottom:50%;right:50%;margin:0 -15px -15px 0}.aplayer .aplayer-pic .aplayer-play svg{position:absolute;top:3px;left:4px;height:20px;width:20px}.aplayer .aplayer-pic .aplayer-pause{width:16px;height:16px;border:2px solid #fff;bottom:4px;right:4px}.aplayer .aplayer-pic .aplayer-pause svg{position:absolute;top:2px;left:2px;height:12px;width:12px}.aplayer .aplayer-info{margin-left:66px;padding:14px 7px 0 10px;height:66px;box-sizing:border-box}.aplayer .aplayer-info .aplayer-music{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin:0 0 13px 5px;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;cursor:default;padding-bottom:2px;height:20px}.aplayer .aplayer-info .aplayer-music .aplayer-title{font-size:14px}.aplayer .aplayer-info .aplayer-music .aplayer-author{font-size:12px;color:#666}.aplayer .aplayer-info .aplayer-controller{position:relative;display:flex}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap{margin:0 0 0 5px;padding:4px 0;cursor:pointer!important;flex:1}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap:hover .aplayer-bar .aplayer-played .aplayer-thumb{transform:scale(1)}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar{position:relative;height:2px;width:100%;background:#cdcdcd}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded{position:absolute;left:0;top:0;bottom:0;background:#aaa;height:2px;transition:all .5s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played{position:absolute;left:0;top:0;bottom:0;height:2px}.aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb{position:absolute;top:0;right:5px;margin-top:-4px;margin-right:-10px;height:10px;width:10px;border-radius:50%;cursor:pointer;transition:all .3s ease-in-out;transform:scale(0)}.aplayer .aplayer-info .aplayer-controller .aplayer-time{position:relative;right:0;bottom:4px;height:17px;color:#999;font-size:11px;padding-left:7px}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-time-inner{vertical-align:middle}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon{cursor:pointer;transition:all .2s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon path{fill:#666}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-loop{margin-right:2px}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon:hover path{fill:#000}.aplayer .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-menu,.aplayer .aplayer-info .aplayer-controller .aplayer-time.aplayer-time-narrow .aplayer-icon-menu,.aplayer .aplayer-info .aplayer-controller .aplayer-time.aplayer-time-narrow .aplayer-icon-mode{display:none}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap{position:relative;display:inline-block;margin-left:3px;cursor:pointer!important}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap:hover .aplayer-volume-bar-wrap{height:40px}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap{position:absolute;bottom:15px;right:-3px;width:25px;height:0;z-index:99;overflow:hidden;transition:all .2s ease-in-out}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap.aplayer-volume-bar-wrap-active{height:40px}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar{position:absolute;bottom:0;right:10px;width:5px;height:35px;background:#aaa;border-radius:2.5px;overflow:hidden}.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap .aplayer-volume-bar-wrap .aplayer-volume-bar .aplayer-volume{position:absolute;bottom:0;right:0;width:5px;transition:all .1s ease}.aplayer .aplayer-info .aplayer-controller .aplayer-loading-icon{display:none}.aplayer .aplayer-info .aplayer-controller .aplayer-loading-icon svg{position:absolute;-webkit-animation:rotate 1s linear infinite;animation:rotate 1s linear infinite}.aplayer .aplayer-lrc{display:none;position:relative;height:30px;text-align:center;overflow:hidden;margin:-10px 0 7px}.aplayer .aplayer-lrc:before{top:0;height:10%;background:linear-gradient(180deg,#fff 0,hsla(0,0%,100%,0));filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#ffffff",endColorstr="#00ffffff",GradientType=0)}.aplayer .aplayer-lrc:after,.aplayer .aplayer-lrc:before{position:absolute;z-index:1;display:block;overflow:hidden;width:100%;content:" "}.aplayer .aplayer-lrc:after{bottom:0;height:33%;background:linear-gradient(180deg,hsla(0,0%,100%,0) 0,hsla(0,0%,100%,.8));filter:progid:DXImageTransform.Microsoft.gradient(startColorstr="#00ffffff",endColorstr="#ccffffff",GradientType=0)}.aplayer .aplayer-lrc p{font-size:12px;color:#666;line-height:16px!important;height:16px!important;padding:0!important;margin:0!important;transition:all .5s ease-out;opacity:.4;overflow:hidden}.aplayer .aplayer-lrc p.aplayer-lrc-current{opacity:1;overflow:visible;height:auto!important;min-height:16px}.aplayer .aplayer-lrc.aplayer-lrc-hide{display:none}.aplayer .aplayer-lrc .aplayer-lrc-contents{width:100%;transition:all .5s ease-out;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;cursor:default}.aplayer .aplayer-list{overflow:auto;transition:all .5s ease;will-change:height;display:none;overflow:hidden;list-style-type:none;margin:0;padding:0;overflow-y:auto}.aplayer .aplayer-list::-webkit-scrollbar{width:5px}.aplayer .aplayer-list::-webkit-scrollbar-thumb{border-radius:3px;background-color:#eee}.aplayer .aplayer-list::-webkit-scrollbar-thumb:hover{background-color:#ccc}.aplayer .aplayer-list li{position:relative;height:32px;line-height:32px;padding:0 15px;font-size:12px;border-top:1px solid #e9e9e9;cursor:pointer;transition:all .2s ease;overflow:hidden;margin:0}.aplayer .aplayer-list li:first-child{border-top:none}.aplayer .aplayer-list li:hover{background:#efefef}.aplayer .aplayer-list li.aplayer-list-light{background:#e9e9e9}.aplayer .aplayer-list li.aplayer-list-light .aplayer-list-cur{display:inline-block}.aplayer .aplayer-list li .aplayer-list-cur{display:none;width:3px;height:22px;position:absolute;left:0;top:5px;cursor:pointer}.aplayer .aplayer-list li .aplayer-list-index{color:#666;margin-right:12px;cursor:pointer}.aplayer .aplayer-list li .aplayer-list-author{color:#666;float:right;cursor:pointer}.aplayer .aplayer-notice{opacity:0;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:12px;border-radius:4px;padding:5px 10px;transition:all .3s ease-in-out;overflow:hidden;color:#fff;pointer-events:none;background-color:#f4f4f5;color:#909399}.aplayer .aplayer-miniswitcher{display:none;position:absolute;top:0;right:0;bottom:0;height:100%;background:#e6e6e6;width:18px;border-radius:0 2px 2px 0}.aplayer .aplayer-miniswitcher .aplayer-icon{height:100%;width:100%;transform:rotateY(180deg);transition:all .3s ease}.aplayer .aplayer-miniswitcher .aplayer-icon path{fill:#666}.aplayer .aplayer-miniswitcher .aplayer-icon:hover path{fill:#000}@-webkit-keyframes aplayer-roll{0%{left:0}to{left:-100%}}@keyframes aplayer-roll{0%{left:0}to{left:-100%}}@-webkit-keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(1turn)}}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(1turn)}}'],sourceRoot:""}]),a.a=o},function(e,a,t){var r=t(2);e.exports=function(e){"use strict";e=e||{};var a="",t=r.$each,i=e.lyrics,n=(e.$value,e.$index,r.$escape);return t(i,(function(e,t){a+="\n    <p",0===t&&(a+=' class="aplayer-lrc-current"'),a+=">",a+=n(e[1]),a+="</p>\n"})),a}},function(e,a,t){"use strict";var r,i=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},n=function(){var e={};return function(a){if(void 0===e[a]){var t=document.querySelector(a);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}e[a]=t}return e[a]}}(),o=[];function l(e){for(var a=-1,t=0;t<o.length;t++)if(o[t].identifier===e){a=t;break}return a}function s(e,a){for(var t={},r=[],i=0;i<e.length;i++){var n=e[i],s=a.base?n[0]+a.base:n[0],p=t[s]||0,c="".concat(s," ").concat(p);t[s]=p+1;var y=l(c),A={css:n[1],media:n[2],sourceMap:n[3]};-1!==y?(o[y].references++,o[y].updater(A)):o.push({identifier:c,updater:f(A,a),references:1}),r.push(c)}return r}function p(e){var a=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var i=t.nc;i&&(r.nonce=i)}if(Object.keys(r).forEach((function(e){a.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(a);else{var o=n(e.insert||"head");if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(a)}return a}var c,y=(c=[],function(e,a){return c[e]=a,c.filter(Boolean).join("\n")});function A(e,a,t,r){var i=t?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=y(a,i);else{var n=document.createTextNode(i),o=e.childNodes;o[a]&&e.removeChild(o[a]),o.length?e.insertBefore(n,o[a]):e.appendChild(n)}}function u(e,a,t){var r=t.css,i=t.media,n=t.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),n&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(n))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var d=null,h=0;function f(e,a){var t,r,i;if(a.singleton){var n=h++;t=d||(d=p(a)),r=A.bind(null,t,n,!1),i=A.bind(null,t,n,!0)}else t=p(a),r=u.bind(null,t,a),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)};return r(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;r(e=a)}else i()}}e.exports=function(e,a){(a=a||{}).singleton||"boolean"==typeof a.singleton||(a.singleton=i());var t=s(e=e||[],a);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<t.length;r++){var i=l(t[r]);o[i].references--}for(var n=s(e,a),p=0;p<t.length;p++){var c=l(t[p]);0===o[c].references&&(o[c].updater(),o.splice(c,1))}t=n}}}},function(e,a,t){"use strict";function r(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var t=[],r=!0,i=!1,n=void 0;try{for(var o,l=e[Symbol.iterator]();!(r=(o=l.next()).done)&&(t.push(o.value),!a||t.length!==a);r=!0);}catch(e){i=!0,n=e}finally{try{r||null==l.return||l.return()}finally{if(i)throw n}}return t}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return i(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return i(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}e.exports=function(e){var a=r(e,4),t=a[1],i=a[3];if("function"==typeof btoa){var n=btoa(unescape(encodeURIComponent(JSON.stringify(i)))),o="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(n),l="/*# ".concat(o," */"),s=i.sources.map((function(e){return"/*# sourceURL=".concat(i.sourceRoot||"").concat(e," */")}));return[t].concat(s).concat([l]).join("\n")}return[t].join("\n")}},function(e,a,t){"use strict";e.exports=function(e){var a=[];return a.toString=function(){return this.map((function(a){var t=e(a);return a[2]?"@media ".concat(a[2]," {").concat(t,"}"):t})).join("")},a.i=function(e,t,r){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(r)for(var n=0;n<this.length;n++){var o=this[n][0];null!=o&&(i[o]=!0)}for(var l=0;l<e.length;l++){var s=[].concat(e[l]);r&&i[s[0]]||(t&&(s[2]?s[2]="".concat(t," and ").concat(s[2]):s[2]=t),a.push(s))}},a}},function(e,a,t){"use strict";(function(e){var r=t(9),i=t(10);function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=setTimeout;function l(e){return Boolean(e&&void 0!==e.length)}function s(){}function p(e){if(!(this instanceof p))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],h(e,this)}function c(e,a){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,p._immediateFn((function(){var t=1===e._state?a.onFulfilled:a.onRejected;if(null!==t){var r;try{r=t(e._value)}catch(e){return void A(a.promise,e)}y(a.promise,r)}else(1===e._state?y:A)(a.promise,e._value)}))):e._deferreds.push(a)}function y(e,a){try{if(a===e)throw new TypeError("A promise cannot be resolved with itself.");if(a&&("object"===n(a)||"function"==typeof a)){var t=a.then;if(a instanceof p)return e._state=3,e._value=a,void u(e);if("function"==typeof t)return void h((r=t,i=a,function(){r.apply(i,arguments)}),e)}e._state=1,e._value=a,u(e)}catch(a){A(e,a)}var r,i}function A(e,a){e._state=2,e._value=a,u(e)}function u(e){2===e._state&&0===e._deferreds.length&&p._immediateFn((function(){e._handled||p._unhandledRejectionFn(e._value)}));for(var a=0,t=e._deferreds.length;a<t;a++)c(e,e._deferreds[a]);e._deferreds=null}function d(e,a,t){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof a?a:null,this.promise=t}function h(e,a){var t=!1;try{e((function(e){t||(t=!0,y(a,e))}),(function(e){t||(t=!0,A(a,e))}))}catch(e){if(t)return;t=!0,A(a,e)}}p.prototype.catch=function(e){return this.then(null,e)},p.prototype.then=function(e,a){var t=new this.constructor(s);return c(this,new d(e,a,t)),t},p.prototype.finally=r.a,p.all=function(e){return new p((function(a,t){if(!l(e))return t(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(e);if(0===r.length)return a([]);var i=r.length;function o(e,l){try{if(l&&("object"===n(l)||"function"==typeof l)){var s=l.then;if("function"==typeof s)return void s.call(l,(function(a){o(e,a)}),t)}r[e]=l,0==--i&&a(r)}catch(e){t(e)}}for(var s=0;s<r.length;s++)o(s,r[s])}))},p.allSettled=i.a,p.resolve=function(e){return e&&"object"===n(e)&&e.constructor===p?e:new p((function(a){a(e)}))},p.reject=function(e){return new p((function(a,t){t(e)}))},p.race=function(e){return new p((function(a,t){if(!l(e))return t(new TypeError("Promise.race accepts an array"));for(var r=0,i=e.length;r<i;r++)p.resolve(e[r]).then(a,t)}))},p._immediateFn="function"==typeof e&&function(a){e(a)}||function(e){o(e,0)},p._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},a.a=p}).call(this,t(27).setImmediate)},function(e,a,t){"use strict";a.a=function(e){var a=this.constructor;return this.then((function(t){return a.resolve(e()).then((function(){return t}))}),(function(t){return a.resolve(e()).then((function(){return a.reject(t)}))}))}},function(e,a,t){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}a.a=function(e){return new this((function(a,t){if(!e||void 0===e.length)return t(new TypeError(r(e)+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var i=Array.prototype.slice.call(e);if(0===i.length)return a([]);var n=i.length;function o(e,t){if(t&&("object"===r(t)||"function"==typeof t)){var l=t.then;if("function"==typeof l)return void l.call(t,(function(a){o(e,a)}),(function(t){i[e]={status:"rejected",reason:t},0==--n&&a(i)}))}i[e]={status:"fulfilled",value:t},0==--n&&a(i)}for(var l=0;l<i.length;l++)o(l,i[l])}))}},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 16 31"><path d="M15.552 15.168q0.448 0.32 0.448 0.832 0 0.448-0.448 0.768l-13.696 8.512q-0.768 0.512-1.312 0.192t-0.544-1.28v-16.448q0-0.96 0.544-1.28t1.312 0.192z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 17 32"><path d="M14.080 4.8q2.88 0 2.88 2.048v18.24q0 2.112-2.88 2.112t-2.88-2.112v-18.24q0-2.048 2.88-2.048zM2.88 4.8q2.88 0 2.88 2.048v18.24q0 2.112-2.88 2.112t-2.88-2.112v-18.24q0-2.048 2.88-2.048z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 28 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8zM20.576 16q0 1.344-0.768 2.528t-2.016 1.664q-0.16 0.096-0.448 0.096-0.448 0-0.8-0.32t-0.32-0.832q0-0.384 0.192-0.64t0.544-0.448 0.608-0.384 0.512-0.64 0.192-1.024-0.192-1.024-0.512-0.64-0.608-0.384-0.544-0.448-0.192-0.64q0-0.48 0.32-0.832t0.8-0.32q0.288 0 0.448 0.096 1.248 0.48 2.016 1.664t0.768 2.528zM25.152 16q0 2.72-1.536 5.056t-4 3.36q-0.256 0.096-0.448 0.096-0.48 0-0.832-0.352t-0.32-0.8q0-0.704 0.672-1.056 1.024-0.512 1.376-0.8 1.312-0.96 2.048-2.4t0.736-3.104-0.736-3.104-2.048-2.4q-0.352-0.288-1.376-0.8-0.672-0.352-0.672-1.056 0-0.448 0.32-0.8t0.8-0.352q0.224 0 0.48 0.096 2.496 1.056 4 3.36t1.536 5.056zM29.728 16q0 4.096-2.272 7.552t-6.048 5.056q-0.224 0.096-0.448 0.096-0.48 0-0.832-0.352t-0.32-0.8q0-0.64 0.704-1.056 0.128-0.064 0.384-0.192t0.416-0.192q0.8-0.448 1.44-0.896 2.208-1.632 3.456-4.064t1.216-5.152-1.216-5.152-3.456-4.064q-0.64-0.448-1.44-0.896-0.128-0.096-0.416-0.192t-0.384-0.192q-0.704-0.416-0.704-1.056 0-0.448 0.32-0.8t0.832-0.352q0.224 0 0.448 0.096 3.776 1.632 6.048 5.056t2.272 7.552z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 28 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8zM20.576 16q0 1.344-0.768 2.528t-2.016 1.664q-0.16 0.096-0.448 0.096-0.448 0-0.8-0.32t-0.32-0.832q0-0.384 0.192-0.64t0.544-0.448 0.608-0.384 0.512-0.64 0.192-1.024-0.192-1.024-0.512-0.64-0.608-0.384-0.544-0.448-0.192-0.64q0-0.48 0.32-0.832t0.8-0.32q0.288 0 0.448 0.096 1.248 0.48 2.016 1.664t0.768 2.528z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 28 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M22.667 4l7 6-7 6 7 6-7 6v-4h-3.653l-3.76-3.76 2.827-2.827 2.587 2.587h2v-8h-2l-12 12h-6v-4h4.347l12-12h3.653v-4zM2.667 8h6l3.76 3.76-2.827 2.827-2.587-2.587h-4.347v-4z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M0.622 18.334h19.54v7.55l11.052-9.412-11.052-9.413v7.549h-19.54v3.725z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 22 32"><path d="M20.8 14.4q0.704 0 1.152 0.48t0.448 1.12-0.48 1.12-1.12 0.48h-19.2q-0.64 0-1.12-0.48t-0.48-1.12 0.448-1.12 1.152-0.48h19.2zM1.6 11.2q-0.64 0-1.12-0.48t-0.48-1.12 0.448-1.12 1.152-0.48h19.2q0.704 0 1.152 0.48t0.448 1.12-0.48 1.12-1.12 0.48h-19.2zM20.8 20.8q0.704 0 1.152 0.48t0.448 1.12-0.48 1.12-1.12 0.48h-19.2q-0.64 0-1.12-0.48t-0.48-1.12 0.448-1.12 1.152-0.48h19.2z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 29 32"><path d="M9.333 9.333h13.333v4l5.333-5.333-5.333-5.333v4h-16v8h2.667v-5.333zM22.667 22.667h-13.333v-4l-5.333 5.333 5.333 5.333v-4h16v-8h-2.667v5.333z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 33 32"><path d="M9.333 9.333h13.333v4l5.333-5.333-5.333-5.333v4h-16v8h2.667v-5.333zM22.667 22.667h-13.333v-4l-5.333 5.333 5.333 5.333v-4h16v-8h-2.667v5.333zM17.333 20v-8h-1.333l-2.667 1.333v1.333h2v5.333h2z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 29 32"><path d="M2.667 7.027l1.707-1.693 22.293 22.293-1.693 1.707-4-4h-11.64v4l-5.333-5.333 5.333-5.333v4h8.973l-8.973-8.973v0.973h-2.667v-3.64l-4-4zM22.667 17.333h2.667v5.573l-2.667-2.667v-2.907zM22.667 6.667v-4l5.333 5.333-5.333 5.333v-4h-10.907l-2.667-2.667h13.573z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M4 16c0-6.6 5.4-12 12-12s12 5.4 12 12c0 1.2-0.8 2-2 2s-2-0.8-2-2c0-4.4-3.6-8-8-8s-8 3.6-8 8 3.6 8 8 8c1.2 0 2 0.8 2 2s-0.8 2-2 2c-6.6 0-12-5.4-12-12z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M22 16l-10.105-10.6-1.895 1.987 8.211 8.613-8.211 8.612 1.895 1.988 8.211-8.613z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M25.468 6.947c-0.326-0.172-0.724-0.151-1.030 0.057l-6.438 4.38v-3.553c0-0.371-0.205-0.71-0.532-0.884-0.326-0.172-0.724-0.151-1.030 0.057l-12 8.164c-0.274 0.186-0.438 0.496-0.438 0.827s0.164 0.641 0.438 0.827l12 8.168c0.169 0.115 0.365 0.174 0.562 0.174 0.16 0 0.321-0.038 0.468-0.116 0.327-0.173 0.532-0.514 0.532-0.884v-3.556l6.438 4.382c0.169 0.115 0.365 0.174 0.562 0.174 0.16 0 0.321-0.038 0.468-0.116 0.327-0.173 0.532-0.514 0.532-0.884v-16.333c0-0.371-0.205-0.71-0.532-0.884z"></path></svg>'},function(e,a){e.exports='<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M26.667 5.333h-21.333c-0 0-0.001 0-0.001 0-1.472 0-2.666 1.194-2.666 2.666 0 0 0 0.001 0 0.001v-0 16c0 0 0 0.001 0 0.001 0 1.472 1.194 2.666 2.666 2.666 0 0 0.001 0 0.001 0h21.333c0 0 0.001 0 0.001 0 1.472 0 2.666-1.194 2.666-2.666 0-0 0-0.001 0-0.001v0-16c0-0 0-0.001 0-0.001 0-1.472-1.194-2.666-2.666-2.666-0 0-0.001 0-0.001 0h0zM5.333 16h5.333v2.667h-5.333v-2.667zM18.667 24h-13.333v-2.667h13.333v2.667zM26.667 24h-5.333v-2.667h5.333v2.667zM26.667 18.667h-13.333v-2.667h13.333v2.667z"></path></svg>'},function(e,a,t){var r=t(2);e.exports=function(e){"use strict";var a="",i=(e=e||{}).options,n=e.cover,o=r.$escape,l=e.icons,s=(arguments[1],function(e){return a+=e}),p=e.getObject;e.theme,e.audio,e.index;return i.fixed?(a+='\n<ol class="aplayer-list',i.listFolded&&(a+=" aplayer-list-hide"),a+='">\n    ',s(t(0)(p({theme:i.theme,audio:i.audio,index:1}))),a+='\n</ol>\n<div class="aplayer-body">\n    <div class="aplayer-pic" style="',n&&(a+="background-image: url(&quot;",a+=o(n),a+="&quot;);"),a+="background-color: ",a+=o(i.theme),a+=';">\n        <div class="aplayer-button aplayer-play">',a+=l.play,a+='</div>\n    </div>\n    <div class="aplayer-info" style="display: none;">\n        <div class="aplayer-music">\n            <span class="aplayer-title">No audio</span>\n            <span class="aplayer-author"></span>\n        </div>\n        <div class="aplayer-controller">\n            <div class="aplayer-bar-wrap">\n                <div class="aplayer-bar">\n                    <div class="aplayer-loaded" style="width: 0"></div>\n                    <div class="aplayer-played" style="width: 0; background: ',a+=o(i.theme),a+=';">\n                        <span class="aplayer-thumb" style="background: ',a+=o(i.theme),a+=';">\n                            <span class="aplayer-loading-icon">',a+=l.loading,a+='</span>\n                        </span>\n                    </div>\n                </div>\n            </div>\n            <div class="aplayer-time">\n                <span class="aplayer-time-inner">\n                    <span class="aplayer-ptime">00:00</span> / <span class="aplayer-dtime">00:00</span>\n                </span>\n                <span class="aplayer-icon aplayer-icon-back">\n                    ',a+=l.skip,a+='\n                </span>\n                <span class="aplayer-icon aplayer-icon-play">\n                    ',a+=l.play,a+='\n                </span>\n                <span class="aplayer-icon aplayer-icon-forward">\n                    ',a+=l.skip,a+='\n                </span>\n                <div class="aplayer-volume-wrap">\n                    <button type="button" class="aplayer-icon aplayer-icon-volume-down">\n                        ',a+=l.volumeDown,a+='\n                    </button>\n                    <div class="aplayer-volume-bar-wrap">\n                        <div class="aplayer-volume-bar">\n                            <div class="aplayer-volume" style="height: 80%; background: ',a+=o(i.theme),a+=';"></div>\n                        </div>\n                    </div>\n                </div>\n                <button type="button" class="aplayer-icon aplayer-icon-order">\n                    ',"list"===i.order?a+=l.orderList:"random"===i.order&&(a+=l.orderRandom),a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-loop">\n                    ',"one"===i.loop?a+=l.loopOne:"all"===i.loop?a+=l.loopAll:"none"===i.loop&&(a+=l.loopNone),a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-menu">\n                    ',a+=l.menu,a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-lrc">\n                    ',a+=l.lrc,a+='\n                </button>\n            </div>\n        </div>\n    </div>\n    <div class="aplayer-notice"></div>\n    <div class="aplayer-miniswitcher"><button class="aplayer-icon">',a+=l.right,a+='</button></div>\n</div>\n<div class="aplayer-lrc">\n    <div class="aplayer-lrc-contents" style="transform: translateY(0); -webkit-transform: translateY(0);"></div>\n</div>\n'):(a+='\n<div class="aplayer-body">\n    <div class="aplayer-pic" style="',n&&(a+="background-image: url(&quot;",a+=o(n),a+="&quot;);"),a+="background-color: ",a+=o(i.theme),a+=';">\n        <div class="aplayer-button aplayer-play">',a+=l.play,a+='</div>\n    </div>\n    <div class="aplayer-info">\n        <div class="aplayer-music">\n            <span class="aplayer-title">No audio</span>\n            <span class="aplayer-author"></span>\n        </div>\n        <div class="aplayer-lrc">\n            <div class="aplayer-lrc-contents" style="transform: translateY(0); -webkit-transform: translateY(0);"></div>\n        </div>\n        <div class="aplayer-controller">\n            <div class="aplayer-bar-wrap">\n                <div class="aplayer-bar">\n                    <div class="aplayer-loaded" style="width: 0"></div>\n                    <div class="aplayer-played" style="width: 0; background: ',a+=o(i.theme),a+=';">\n                        <span class="aplayer-thumb" style="background: ',a+=o(i.theme),a+=';">\n                            <span class="aplayer-loading-icon">',a+=l.loading,a+='</span>\n                        </span>\n                    </div>\n                </div>\n            </div>\n            <div class="aplayer-time">\n                <span class="aplayer-time-inner">\n                    <span class="aplayer-ptime">00:00</span> / <span class="aplayer-dtime">00:00</span>\n                </span>\n                <span class="aplayer-icon aplayer-icon-back">\n                    ',a+=l.skip,a+='\n                </span>\n                <span class="aplayer-icon aplayer-icon-play">\n                    ',a+=l.play,a+='\n                </span>\n                <span class="aplayer-icon aplayer-icon-forward">\n                    ',a+=l.skip,a+='\n                </span>\n                <div class="aplayer-volume-wrap">\n                    <button type="button" class="aplayer-icon aplayer-icon-volume-down">\n                        ',a+=l.volumeDown,a+='\n                    </button>\n                    <div class="aplayer-volume-bar-wrap">\n                        <div class="aplayer-volume-bar">\n                            <div class="aplayer-volume" style="height: 80%; background: ',a+=o(i.theme),a+=';"></div>\n                        </div>\n                    </div>\n                </div>\n                <button type="button" class="aplayer-icon aplayer-icon-order">\n                    ',"list"===i.order?a+=l.orderList:"random"===i.order&&(a+=l.orderRandom),a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-loop">\n                    ',"one"===i.loop?a+=l.loopOne:"all"===i.loop?a+=l.loopAll:"none"===i.loop&&(a+=l.loopNone),a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-menu">\n                    ',a+=l.menu,a+='\n                </button>\n                <button type="button" class="aplayer-icon aplayer-icon-lrc">\n                    ',a+=l.lrc,a+='\n                </button>\n            </div>\n        </div>\n    </div>\n    <div class="aplayer-notice"></div>\n    <div class="aplayer-miniswitcher"><button class="aplayer-icon">',a+=l.right,a+='</button></div>\n</div>\n<ol class="aplayer-list',i.listFolded&&(a+=" aplayer-list-hide"),a+='">\n    ',s(t(0)(p({theme:i.theme,audio:i.audio,index:1}))),a+="\n</ol>\n"),a}},function(e,a,t){(function(e){var r=void 0!==e&&e||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;function n(e,a){this._id=e,this._clearFn=a}a.setTimeout=function(){return new n(i.call(setTimeout,r,arguments),clearTimeout)},a.setInterval=function(){return new n(i.call(setInterval,r,arguments),clearInterval)},a.clearTimeout=a.clearInterval=function(e){e&&e.close()},n.prototype.unref=n.prototype.ref=function(){},n.prototype.close=function(){this._clearFn.call(r,this._id)},a.enroll=function(e,a){clearTimeout(e._idleTimeoutId),e._idleTimeout=a},a.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},a._unrefActive=a.active=function(e){clearTimeout(e._idleTimeoutId);var a=e._idleTimeout;a>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),a))},t(28),a.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,a.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,t(1))},function(e,a,t){(function(e,a){!function(e,t){"use strict";if(!e.setImmediate){var r,i,n,o,l,s=1,p={},c=!1,y=e.document,A=Object.getPrototypeOf&&Object.getPrototypeOf(e);A=A&&A.setTimeout?A:e,"[object process]"==={}.toString.call(e.process)?r=function(e){a.nextTick((function(){d(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var a=!0,t=e.onmessage;return e.onmessage=function(){a=!1},e.postMessage("","*"),e.onmessage=t,a}}()?e.MessageChannel?((n=new MessageChannel).port1.onmessage=function(e){d(e.data)},r=function(e){n.port2.postMessage(e)}):y&&"onreadystatechange"in y.createElement("script")?(i=y.documentElement,r=function(e){var a=y.createElement("script");a.onreadystatechange=function(){d(e),a.onreadystatechange=null,i.removeChild(a),a=null},i.appendChild(a)}):r=function(e){setTimeout(d,0,e)}:(o="setImmediate$"+Math.random()+"$",l=function(a){a.source===e&&"string"==typeof a.data&&0===a.data.indexOf(o)&&d(+a.data.slice(o.length))},e.addEventListener?e.addEventListener("message",l,!1):e.attachEvent("onmessage",l),r=function(a){e.postMessage(o+a,"*")}),A.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var a=new Array(arguments.length-1),t=0;t<a.length;t++)a[t]=arguments[t+1];var i={callback:e,args:a};return p[s]=i,r(s),s++},A.clearImmediate=u}function u(e){delete p[e]}function d(e){if(c)setTimeout(d,0,e);else{var a=p[e];if(a){c=!0;try{!function(e){var a=e.callback,r=e.args;switch(r.length){case 0:a();break;case 1:a(r[0]);break;case 2:a(r[0],r[1]);break;case 3:a(r[0],r[1],r[2]);break;default:a.apply(t,r)}}(a)}finally{u(e),c=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,t(1),t(29))},function(e,a){var t,r,i=e.exports={};function n(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function l(e){if(t===setTimeout)return setTimeout(e,0);if((t===n||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(a){try{return t.call(null,e,0)}catch(a){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:n}catch(e){t=n}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var s,p=[],c=!1,y=-1;function A(){c&&s&&(c=!1,s.length?p=s.concat(p):y=-1,p.length&&u())}function u(){if(!c){var e=l(A);c=!0;for(var a=p.length;a;){for(s=p,p=[];++y<a;)s&&s[y].run();y=-1,a=p.length}s=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(a){try{return r.call(null,e)}catch(a){return r.call(this,e)}}}(e)}}function d(e,a){this.fun=e,this.array=a}function h(){}i.nextTick=function(e){var a=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)a[t-1]=arguments[t];p.push(new d(e,a)),1!==p.length||c||l(u)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,a,t){"use strict";(function(a){var t="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==a?a:{},r=Object.create(t),i=/["&'<>]/;r.$escape=function(e){return function(e){var a=""+e,t=i.exec(a);if(!t)return e;var r="",n=void 0,o=void 0,l=void 0;for(n=t.index,o=0;n<a.length;n++){switch(a.charCodeAt(n)){case 34:l="&#34;";break;case 38:l="&#38;";break;case 39:l="&#39;";break;case 60:l="&#60;";break;case 62:l="&#62;";break;default:continue}o!==n&&(r+=a.substring(o,n)),o=n+1,r+=l}return o!==n?r+a.substring(o,n):r}(function e(a){"string"!=typeof a&&(a=null==a?"":"function"==typeof a?e(a.call(a)):JSON.stringify(a));return a}(e))},r.$each=function(e,a){if(Array.isArray(e))for(var t=0,r=e.length;t<r;t++)a(e[t],t);else for(var i in e)a(e[i],i)},e.exports=r}).call(this,t(1))},function(e,a,t){"use strict";t.r(a);var r=t(5),i=t.n(r),n=t(3),o={insert:"head",singleton:!1},l=(i()(n.a,o),n.a.locals,t(8));function s(e){return function(e){if(Array.isArray(e))return p(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,a){if(!e)return;if("string"==typeof e)return p(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return p(e,a)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var c=/mobile/i.test(window.navigator.userAgent),y={secondToTime:function(e){var a=Math.floor(e/3600),t=Math.floor((e-3600*a)/60),r=Math.floor(e-3600*a-60*t);return(a>0?[a,t,r]:[t,r]).map((function(e){return e<10?"0"+e:""+e})).join(":")},isMobile:c,storage:{set:function(e,a){localStorage.setItem(e,a)},get:function(e){return localStorage.getItem(e)}},nameMap:{dragStart:c?"touchstart":"mousedown",dragMove:c?"touchmove":"mousemove",dragEnd:c?"touchend":"mouseup"},randomOrder:function(e){return function(e){for(var a=e.length-1;a>=0;a--){var t=Math.floor(Math.random()*(a+1)),r=e[t];e[t]=e[a],e[a]=r}return e}(s(Array(e)).map((function(e,a){return a})))}},A=t(11),u=t.n(A),d=t(12),h=t.n(d),f=t(13),m=t.n(f),C=t(14),v=t.n(C),g=t(15),b=t.n(g),w=t(16),x=t.n(w),B=t(17),k=t.n(B),M=t(18),T=t.n(M),S=t(19),q=t.n(S),L=t(20),U=t.n(L),E=t(21),O=t.n(E),W=t(22),z=t.n(W),H=t(23),I=t.n(H),j=t(24),_=t.n(j),F=t(25),P=t.n(F),Y={play:u.a,pause:h.a,volumeUp:m.a,volumeDown:v.a,volumeOff:b.a,orderRandom:x.a,orderList:k.a,menu:T.a,loopAll:q.a,loopOne:U.a,loopNone:O.a,loading:z.a,right:I.a,skip:_.a,lrc:P.a},N=function(e){var a={container:e.element||document.getElementsByClassName("aplayer")[0],mini:e.narrow||e.fixed||!1,fixed:!1,autoplay:!1,mutex:!0,lrcType:e.showlrc||e.lrc||0,preload:"metadata",theme:"#b7daff",loop:"all",order:"list",volume:.7,listFolded:e.fixed,listMaxHeight:e.listmaxheight||250,audio:e.music||[],storageName:"aplayer-setting"};for(var t in a)a.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=a[t]);return e.listMaxHeight=parseFloat(e.listMaxHeight),"[object Array]"!==Object.prototype.toString.call(e.audio)&&(e.audio=[e.audio]),e.audio.map((function(e){return e.name=e.name||e.title||"Audio name",e.artist=e.artist||e.author||"Audio artist",e.cover=e.cover||e.pic,e.type=e.type||"normal",e})),e.audio.length<=1&&"one"===e.loop&&(e.loop="all"),e},D=t(26),Q=t.n(D);function R(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var G=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.container=a.container,this.options=a.options,this.randomOrder=a.randomOrder,this.init()}var a,t,r;return a=e,(t=[{key:"init",value:function(){var e="";this.options.audio.length&&(e="random"===this.options.order?this.options.audio[this.randomOrder[0]].cover:this.options.audio[0].cover),this.container.innerHTML=Q()({options:this.options,icons:Y,cover:e,getObject:function(e){return e}}),this.lrc=this.container.querySelector(".aplayer-lrc-contents"),this.lrcWrap=this.container.querySelector(".aplayer-lrc"),this.ptime=this.container.querySelector(".aplayer-ptime"),this.info=this.container.querySelector(".aplayer-info"),this.time=this.container.querySelector(".aplayer-time"),this.barWrap=this.container.querySelector(".aplayer-bar-wrap"),this.button=this.container.querySelector(".aplayer-button"),this.body=this.container.querySelector(".aplayer-body"),this.list=this.container.querySelector(".aplayer-list"),this.listCurs=this.container.querySelectorAll(".aplayer-list-cur"),this.played=this.container.querySelector(".aplayer-played"),this.loaded=this.container.querySelector(".aplayer-loaded"),this.thumb=this.container.querySelector(".aplayer-thumb"),this.volume=this.container.querySelector(".aplayer-volume"),this.volumeBar=this.container.querySelector(".aplayer-volume-bar"),this.volumeButton=this.container.querySelector(".aplayer-time button"),this.volumeBarWrap=this.container.querySelector(".aplayer-volume-bar-wrap"),this.loop=this.container.querySelector(".aplayer-icon-loop"),this.order=this.container.querySelector(".aplayer-icon-order"),this.menu=this.container.querySelector(".aplayer-icon-menu"),this.pic=this.container.querySelector(".aplayer-pic"),this.title=this.container.querySelector(".aplayer-title"),this.author=this.container.querySelector(".aplayer-author"),this.dtime=this.container.querySelector(".aplayer-dtime"),this.notice=this.container.querySelector(".aplayer-notice"),this.miniSwitcher=this.container.querySelector(".aplayer-miniswitcher"),this.skipBackButton=this.container.querySelector(".aplayer-icon-back"),this.skipForwardButton=this.container.querySelector(".aplayer-icon-forward"),this.skipPlayButton=this.container.querySelector(".aplayer-icon-play"),this.lrcButton=this.container.querySelector(".aplayer-icon-lrc")}}])&&R(a.prototype,t),r&&R(a,r),e}();function $(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var K=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.elements={},this.elements.volume=a.volume,this.elements.played=a.played,this.elements.loaded=a.loaded}var a,t,r;return a=e,(t=[{key:"set",value:function(e,a,t){a=Math.max(a,0),a=Math.min(a,1),this.elements[e].style[t]=100*a+"%"}},{key:"get",value:function(e,a){return parseFloat(this.elements[e].style[a])/100}}])&&$(a.prototype,t),r&&$(a,r),e}();function X(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var J=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.storageName=a.options.storageName,this.data=JSON.parse(y.storage.get(this.storageName)),this.data||(this.data={}),this.data.volume=this.data.volume||a.options.volume}var a,t,r;return a=e,(t=[{key:"get",value:function(e){return this.data[e]}},{key:"set",value:function(e,a){this.data[e]=a,y.storage.set(this.storageName,JSON.stringify(this.data))}}])&&X(a.prototype,t),r&&X(a,r),e}(),V=t(4),Z=t.n(V);function ee(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ae=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.container=a.container,this.async=a.async,this.player=a.player,this.parsed=[],this.index=0,this.current=[]}var a,t,r;return a=e,(t=[{key:"show",value:function(){this.player.events.trigger("lrcshow"),this.player.template.lrcWrap.classList.remove("aplayer-lrc-hide")}},{key:"hide",value:function(){this.player.events.trigger("lrchide"),this.player.template.lrcWrap.classList.add("aplayer-lrc-hide")}},{key:"toggle",value:function(){this.player.template.lrcWrap.classList.contains("aplayer-lrc-hide")?this.show():this.hide()}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.player.audio.currentTime;if(this.index>this.current.length-1||e<this.current[this.index][0]||!this.current[this.index+1]||e>=this.current[this.index+1][0])for(var a=0;a<this.current.length;a++)e>=this.current[a][0]&&(!this.current[a+1]||e<this.current[a+1][0])&&(this.index=a,this.container.style.transform="translateY(".concat(16*-this.index,"px)"),this.container.style.webkitTransform="translateY(".concat(16*-this.index,"px)"),this.container.getElementsByClassName("aplayer-lrc-current")[0].classList.remove("aplayer-lrc-current"),this.container.getElementsByTagName("p")[a].classList.add("aplayer-lrc-current"))}},{key:"switch",value:function(e){var a=this;if(!this.parsed[e])if(this.async){this.parsed[e]=[["00:00","Loading"]];var t=new XMLHttpRequest;t.onreadystatechange=function(){e===a.player.list.index&&4===t.readyState&&(t.status>=200&&t.status<300||304===t.status?a.parsed[e]=a.parse(t.responseText):(a.player.notice("LRC file request fails: status ".concat(t.status)),a.parsed[e]=[["00:00","Not available"]]),a.container.innerHTML=Z()({lyrics:a.parsed[e]}),a.update(0),a.current=a.parsed[e])};var r=this.player.list.audios[e].lrc;t.open("get",r,!0),t.send(null)}else this.player.list.audios[e].lrc?this.parsed[e]=this.parse(this.player.list.audios[e].lrc):this.parsed[e]=[["00:00","Not available"]];this.container.innerHTML=Z()({lyrics:this.parsed[e]}),this.current=this.parsed[e],this.update(0)}},{key:"parse",value:function(e){if(e){for(var a=(e=e.replace(/([^\]^\n])\[/g,(function(e,a){return a+"\n["}))).split("\n"),t=[],r=a.length,i=0;i<r;i++){var n=a[i].match(/\[(\d{2}):(\d{2})(\.(\d{2,3}))?]/g),o=a[i].replace(/.*\[(\d{2}):(\d{2})(\.(\d{2,3}))?]/g,"").replace(/<(\d{2}):(\d{2})(\.(\d{2,3}))?>/g,"").replace(/^\s+|\s+$/g,"");if(n)for(var l=n.length,s=0;s<l;s++){var p=/\[(\d{2}):(\d{2})(\.(\d{2,3}))?]/.exec(n[s]),c=60*p[1]+parseInt(p[2])+(p[4]?parseInt(p[4])/(2===(p[4]+"").length?100:1e3):0);t.push([c,o])}}return(t=t.filter((function(e){return e[1]}))).sort((function(e,a){return e[0]-a[0]})),t}return[]}},{key:"remove",value:function(e){this.parsed.splice(e,1)}},{key:"clear",value:function(){this.parsed=[],this.container.innerHTML=""}}])&&ee(a.prototype,t),r&&ee(a,r),e}();function te(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var re=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.player=a,this.initPlayButton(),this.initPlayBar(),this.initOrderButton(),this.initLoopButton(),this.initMenuButton(),y.isMobile||this.initVolumeButton(),this.initMiniSwitcher(),this.initSkipButton(),this.initLrcButton()}var a,t,r;return a=e,(t=[{key:"initPlayButton",value:function(){var e=this;this.player.template.pic.addEventListener("click",(function(){e.player.toggle()}))}},{key:"initPlayBar",value:function(){var e=this,a=function(a){var t=((a.clientX||a.changedTouches[0].clientX)-e.player.template.barWrap.getBoundingClientRect().left)/e.player.template.barWrap.clientWidth;t=Math.max(t,0),t=Math.min(t,1),e.player.bar.set("played",t,"width"),e.player.lrc&&e.player.lrc.update(t*e.player.duration),e.player.template.ptime.innerHTML=y.secondToTime(t*e.player.duration)},t=function t(r){document.removeEventListener(y.nameMap.dragEnd,t),document.removeEventListener(y.nameMap.dragMove,a);var i=((r.clientX||r.changedTouches[0].clientX)-e.player.template.barWrap.getBoundingClientRect().left)/e.player.template.barWrap.clientWidth;i=Math.max(i,0),i=Math.min(i,1),e.player.bar.set("played",i,"width"),e.player.seek(i*e.player.duration),e.player.disableTimeupdate=!1};this.player.template.barWrap.addEventListener(y.nameMap.dragStart,(function(){e.player.disableTimeupdate=!0,document.addEventListener(y.nameMap.dragMove,a),document.addEventListener(y.nameMap.dragEnd,t)}))}},{key:"initVolumeButton",value:function(){var e=this;this.player.template.volumeButton.addEventListener("click",(function(){e.player.audio.muted?e.player.volume(e.player.audio.volume,!0):(e.player.audio.muted=!0,e.player.switchVolumeIcon(),e.player.bar.set("volume",0,"height"))}));var a=function(a){var t=1-((a.clientY||a.changedTouches[0].clientY)-e.player.template.volumeBar.getBoundingClientRect().top)/e.player.template.volumeBar.clientHeight;t=Math.max(t,0),t=Math.min(t,1),e.player.volume(t)},t=function t(r){e.player.template.volumeBarWrap.classList.remove("aplayer-volume-bar-wrap-active"),document.removeEventListener(y.nameMap.dragEnd,t),document.removeEventListener(y.nameMap.dragMove,a);var i=1-((r.clientY||r.changedTouches[0].clientY)-e.player.template.volumeBar.getBoundingClientRect().top)/e.player.template.volumeBar.clientHeight;i=Math.max(i,0),i=Math.min(i,1),e.player.volume(i)};this.player.template.volumeBarWrap.addEventListener(y.nameMap.dragStart,(function(){e.player.template.volumeBarWrap.classList.add("aplayer-volume-bar-wrap-active"),document.addEventListener(y.nameMap.dragMove,a),document.addEventListener(y.nameMap.dragEnd,t)}))}},{key:"initOrderButton",value:function(){var e=this;this.player.template.order.addEventListener("click",(function(){"list"===e.player.options.order?(e.player.options.order="random",e.player.template.order.innerHTML=Y.orderRandom):"random"===e.player.options.order&&(e.player.options.order="list",e.player.template.order.innerHTML=Y.orderList)}))}},{key:"initLoopButton",value:function(){var e=this;this.player.template.loop.addEventListener("click",(function(){e.player.list.audios.length>1?"one"===e.player.options.loop?(e.player.options.loop="none",e.player.template.loop.innerHTML=Y.loopNone):"none"===e.player.options.loop?(e.player.options.loop="all",e.player.template.loop.innerHTML=Y.loopAll):"all"===e.player.options.loop&&(e.player.options.loop="one",e.player.template.loop.innerHTML=Y.loopOne):"one"===e.player.options.loop||"all"===e.player.options.loop?(e.player.options.loop="none",e.player.template.loop.innerHTML=Y.loopNone):"none"===e.player.options.loop&&(e.player.options.loop="all",e.player.template.loop.innerHTML=Y.loopAll)}))}},{key:"initMenuButton",value:function(){var e=this;this.player.template.menu.addEventListener("click",(function(){e.player.list.toggle()}))}},{key:"initMiniSwitcher",value:function(){var e=this;this.player.template.miniSwitcher.addEventListener("click",(function(){e.player.setMode("mini"===e.player.mode?"normal":"mini")}))}},{key:"initSkipButton",value:function(){var e=this;this.player.template.skipBackButton.addEventListener("click",(function(){e.player.skipBack()})),this.player.template.skipForwardButton.addEventListener("click",(function(){e.player.skipForward()})),this.player.template.skipPlayButton.addEventListener("click",(function(){e.player.toggle()}))}},{key:"initLrcButton",value:function(){var e=this;this.player.template.lrcButton.addEventListener("click",(function(){e.player.template.lrcButton.classList.contains("aplayer-icon-lrc-inactivity")?(e.player.template.lrcButton.classList.remove("aplayer-icon-lrc-inactivity"),e.player.lrc&&e.player.lrc.show()):(e.player.template.lrcButton.classList.add("aplayer-icon-lrc-inactivity"),e.player.lrc&&e.player.lrc.hide())}))}}])&&te(a.prototype,t),r&&te(a,r),e}();function ie(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ne=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.player=a,window.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)},this.types=["loading"],this.init()}var a,t,r;return a=e,(t=[{key:"init",value:function(){var e=this;this.types.forEach((function(a){e["init".concat(a,"Checker")]()}))}},{key:"initloadingChecker",value:function(){var e=this,a=0,t=0,r=!1;this.loadingChecker=setInterval((function(){e.enableloadingChecker&&(t=e.player.audio.currentTime,r||t!==a||e.player.audio.paused||(e.player.container.classList.add("aplayer-loading"),r=!0),r&&t>a&&!e.player.audio.paused&&(e.player.container.classList.remove("aplayer-loading"),r=!1),a=t)}),100)}},{key:"enable",value:function(e){this["enable".concat(e,"Checker")]=!0,"fps"===e&&this.initfpsChecker()}},{key:"disable",value:function(e){this["enable".concat(e,"Checker")]=!1}},{key:"destroy",value:function(){var e=this;this.types.forEach((function(a){e["enable".concat(a,"Checker")]=!1,e["".concat(a,"Checker")]&&clearInterval(e["".concat(a,"Checker")])}))}}])&&ie(a.prototype,t),r&&ie(a,r),e}();function oe(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var le=function(){function e(){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.events={},this.audioEvents=["abort","canplay","canplaythrough","durationchange","emptied","ended","error","loadeddata","loadedmetadata","loadstart","mozaudioavailable","pause","play","playing","progress","ratechange","seeked","seeking","stalled","suspend","timeupdate","volumechange","waiting"],this.playerEvents=["destroy","listshow","listhide","listadd","listremove","listswitch","listclear","noticeshow","noticehide","lrcshow","lrchide"]}var a,t,r;return a=e,(t=[{key:"on",value:function(e,a){this.type(e)&&"function"==typeof a&&(this.events[e]||(this.events[e]=[]),this.events[e].push(a))}},{key:"trigger",value:function(e,a){if(this.events[e]&&this.events[e].length)for(var t=0;t<this.events[e].length;t++)this.events[e][t](a)}},{key:"type",value:function(e){return-1!==this.playerEvents.indexOf(e)?"player":-1!==this.audioEvents.indexOf(e)?"audio":(console.error("Unknown event name: ".concat(e)),null)}}])&&oe(a.prototype,t),r&&oe(a,r),e}(),se=t(0),pe=t.n(se);function ce(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ye=function(){function e(a){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.player=a,this.index=0,this.audios=this.player.options.audio,this.showing=!0,this.player.template.list.style.height="".concat(Math.min(this.player.template.list.scrollHeight,this.player.options.listMaxHeight),"px"),this.bindEvents()}var a,t,r;return a=e,(t=[{key:"bindEvents",value:function(){var e=this;this.player.template.list.addEventListener("click",(function(a){var t;t="LI"===a.target.tagName.toUpperCase()?a.target:a.target.parentElement;var r=parseInt(t.getElementsByClassName("aplayer-list-index")[0].innerHTML)-1;r!==e.index?(e.switch(r),e.player.play()):e.player.toggle()}))}},{key:"show",value:function(){this.showing=!0,this.player.template.list.scrollTop=33*this.index,this.player.template.list.style.height="".concat(Math.min(this.player.template.list.scrollHeight,this.player.options.listMaxHeight),"px"),this.player.events.trigger("listshow")}},{key:"hide",value:function(){var e=this;this.showing=!1,this.player.template.list.style.height="".concat(Math.min(this.player.template.list.scrollHeight,this.player.options.listMaxHeight),"px"),setTimeout((function(){e.player.template.list.style.height="0px",e.player.events.trigger("listhide")}),0)}},{key:"toggle",value:function(){this.showing?this.hide():this.show()}},{key:"add",value:function(e){this.player.events.trigger("listadd",{audios:e}),"[object Array]"!==Object.prototype.toString.call(e)&&(e=[e]),e.map((function(e){return e.name=e.name||e.title||"Audio name",e.artist=e.artist||e.author||"Audio artist",e.cover=e.cover||e.pic,e.type=e.type||"normal",e}));var a=!(this.audios.length>1),t=0===this.audios.length;this.player.template.list.innerHTML+=pe()({theme:this.player.options.theme,audio:e,index:this.audios.length+1}),this.audios=this.audios.concat(e),a&&this.audios.length>1&&this.player.container.classList.add("aplayer-withlist"),this.player.randomOrder=y.randomOrder(this.audios.length),this.player.template.listCurs=this.player.container.querySelectorAll(".aplayer-list-cur"),this.player.template.listCurs[this.audios.length-1].style.backgroundColor=e.theme||this.player.options.theme,t&&("random"===this.player.options.order?this.switch(this.player.randomOrder[0]):this.switch(0))}},{key:"remove",value:function(e){if(this.player.events.trigger("listremove",{index:e}),this.audios[e])if(this.audios.length>1){var a=this.player.container.querySelectorAll(".aplayer-list li");a[e].remove(),this.audios.splice(e,1),this.player.lrc&&this.player.lrc.remove(e),e===this.index&&(this.audios[e]?this.switch(e):this.switch(e-1)),this.index>e&&this.index--;for(var t=e;t<a.length;t++)a[t].getElementsByClassName("aplayer-list-index")[0].textContent=t;1===this.audios.length&&this.player.container.classList.remove("aplayer-withlist"),this.player.template.listCurs=this.player.container.querySelectorAll(".aplayer-list-cur")}else this.clear()}},{key:"switch",value:function(e){if(this.player.events.trigger("listswitch",{index:e}),void 0!==e&&this.audios[e]){this.index=e;var a=this.audios[this.index];this.player.template.pic.style.backgroundImage=a.cover?"url('".concat(a.cover,"')"):"",this.player.theme(this.audios[this.index].theme||this.player.options.theme,this.index,!1),this.player.template.title.innerHTML=a.name,this.player.template.author.innerHTML=a.artist?" - "+a.artist:"";var t=this.player.container.getElementsByClassName("aplayer-list-light")[0];t&&t.classList.remove("aplayer-list-light"),this.player.container.querySelectorAll(".aplayer-list li")[this.index].classList.add("aplayer-list-light"),this.player.template.list.scrollTop=33*this.index,this.player.setAudio(a),this.player.lrc&&this.player.lrc.switch(this.index),this.player.lrc&&this.player.lrc.update(0),1!==this.player.duration&&(this.player.template.dtime.innerHTML=y.secondToTime(this.player.duration))}}},{key:"clear",value:function(){this.player.events.trigger("listclear"),this.index=0,this.player.container.classList.remove("aplayer-withlist"),this.player.pause(),this.audios=[],this.player.lrc&&this.player.lrc.clear(),this.player.audio.src="",this.player.template.list.innerHTML="",this.player.template.pic.style.backgroundImage="",this.player.theme(this.player.options.theme,this.index,!1),this.player.template.title.innerHTML="No audio",this.player.template.author.innerHTML="",this.player.bar.set("loaded",0,"width"),this.player.template.dtime.innerHTML=y.secondToTime(0)}}])&&ce(a.prototype,t),r&&ce(a,r),e}();function Ae(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ue=[],de=function(){function e(a){if(function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.options=N(a),this.container=this.options.container,this.paused=!0,this.playedPromise=l.a.resolve(),this.mode="normal",this.randomOrder=y.randomOrder(this.options.audio.length),this.container.classList.add("aplayer"),this.options.lrcType&&!this.options.fixed&&this.container.classList.add("aplayer-withlrc"),this.options.audio.length>1&&this.container.classList.add("aplayer-withlist"),y.isMobile&&this.container.classList.add("aplayer-mobile"),this.arrow=this.container.offsetWidth<=300,this.arrow&&this.container.classList.add("aplayer-arrow"),2===this.options.lrcType||!0===this.options.lrcType)for(var t=this.container.getElementsByClassName("aplayer-lrc-content"),r=0;r<t.length;r++)this.options.audio[r]&&(this.options.audio[r].lrc=t[r].innerHTML);this.template=new G({container:this.container,options:this.options,randomOrder:this.randomOrder}),this.options.fixed&&(this.container.classList.add("aplayer-fixed"),this.template.body.style.width=this.template.body.offsetWidth-18+"px"),this.options.mini&&(this.setMode("mini"),this.template.info.style.display="block"),this.template.info.offsetWidth<200&&this.template.time.classList.add("aplayer-time-narrow"),this.options.lrcType&&(this.lrc=new ae({container:this.template.lrc,async:3===this.options.lrcType,player:this})),this.events=new le,this.storage=new J(this),this.bar=new K(this.template),this.controller=new re(this),this.timer=new ne(this),this.list=new ye(this),this.initAudio(),this.bindEvents(),"random"===this.options.order?this.list.switch(this.randomOrder[0]):this.list.switch(0),this.options.autoplay&&this.play(),ue.push(this)}var a,t,r;return a=e,r=[{key:"version",get:function(){return"1.10.1"}}],(t=[{key:"initAudio",value:function(){var e=this;this.audio=document.createElement("audio"),this.audio.preload=this.options.preload;for(var a=function(a){e.audio.addEventListener(e.events.audioEvents[a],(function(t){e.events.trigger(e.events.audioEvents[a],t)}))},t=0;t<this.events.audioEvents.length;t++)a(t);this.volume(this.storage.get("volume"),!0)}},{key:"bindEvents",value:function(){var e,a=this;this.on("play",(function(){a.paused&&a.setUIPlaying()})),this.on("pause",(function(){a.paused||a.setUIPaused()})),this.on("timeupdate",(function(){if(!a.disableTimeupdate){a.bar.set("played",a.audio.currentTime/a.duration,"width"),a.lrc&&a.lrc.update();var e=y.secondToTime(a.audio.currentTime);a.template.ptime.innerHTML!==e&&(a.template.ptime.innerHTML=e)}})),this.on("durationchange",(function(){1!==a.duration&&(a.template.dtime.innerHTML=y.secondToTime(a.duration))})),this.on("loadedmetadata",(function(){a.seek(0),a.paused||a.audio.play()})),this.on("canplay",(function(){var e=a.audio.buffered.length?a.audio.buffered.end(a.audio.buffered.length-1)/a.duration:0;a.bar.set("loaded",e,"width")})),this.on("progress",(function(){var e=a.audio.buffered.length?a.audio.buffered.end(a.audio.buffered.length-1)/a.duration:0;a.bar.set("loaded",e,"width")})),this.on("error",(function(){a.list.audios.length>1?(a.notice("An audio error has occurred, player will skip forward in 2 seconds."),e=setTimeout((function(){a.skipForward(),a.paused||a.play()}),2e3)):1===a.list.audios.length&&a.notice("An audio error has occurred.")})),this.events.on("listswitch",(function(){e&&clearTimeout(e)})),this.on("ended",(function(){"none"===a.options.loop?"list"===a.options.order?a.list.index<a.list.audios.length-1?(a.list.switch((a.list.index+1)%a.list.audios.length),a.play()):(a.list.switch((a.list.index+1)%a.list.audios.length),a.pause()):"random"===a.options.order&&(a.randomOrder.indexOf(a.list.index)<a.randomOrder.length-1?(a.list.switch(a.nextIndex()),a.play()):(a.list.switch(a.nextIndex()),a.pause())):"one"===a.options.loop?(a.list.switch(a.list.index),a.play()):"all"===a.options.loop&&(a.skipForward(),a.play())}))}},{key:"setAudio",value:function(e){this.hls&&(this.hls.destroy(),this.hls=null);var a=e.type;this.options.customAudioType&&this.options.customAudioType[a]?"[object Function]"===Object.prototype.toString.call(this.options.customAudioType[a])?this.options.customAudioType[a](this.audio,e,this):console.error("Illegal customType: ".concat(a)):(a&&"auto"!==a||(a=/m3u8(#|\?|$)/i.exec(e.url)?"hls":"normal"),"hls"===a?window.Hls.isSupported()?(this.hls=new window.Hls,this.hls.loadSource(e.url),this.hls.attachMedia(this.audio)):this.audio.canPlayType("application/x-mpegURL")||this.audio.canPlayType("application/vnd.apple.mpegURL")?this.audio.src=e.url:this.notice("Error: HLS is not supported."):"normal"===a&&(this.audio.src=e.url))}},{key:"theme",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.list.audios[this.list.index].theme||this.options.theme,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.list.index,t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t&&this.list.audios[a]&&(this.list.audios[a].theme=e),this.template.listCurs[a]&&(this.template.listCurs[a].style.backgroundColor=e),a===this.list.index&&(this.template.pic.style.backgroundColor=e,this.template.played.style.background=e,this.template.thumb.style.background=e,this.template.volume.style.background=e)}},{key:"seek",value:function(e){e=Math.max(e,0),e=Math.min(e,this.duration),this.audio.currentTime=e,this.bar.set("played",e/this.duration,"width"),this.template.ptime.innerHTML=y.secondToTime(e)}},{key:"setUIPlaying",value:function(){var e=this;if(this.paused&&(this.paused=!1,this.template.button.classList.remove("aplayer-play"),this.template.button.classList.add("aplayer-pause"),this.template.button.innerHTML="",setTimeout((function(){e.template.button.innerHTML=Y.pause}),100),this.template.skipPlayButton.innerHTML=Y.pause),this.timer.enable("loading"),this.options.mutex)for(var a=0;a<ue.length;a++)this!==ue[a]&&ue[a].pause()}},{key:"play",value:function(){var e=this;this.setUIPlaying();var a=this.audio.play();a&&a.catch((function(a){console.warn(a),"NotAllowedError"===a.name&&e.setUIPaused()}))}},{key:"setUIPaused",value:function(){var e=this;this.paused||(this.paused=!0,this.template.button.classList.remove("aplayer-pause"),this.template.button.classList.add("aplayer-play"),this.template.button.innerHTML="",setTimeout((function(){e.template.button.innerHTML=Y.play}),100),this.template.skipPlayButton.innerHTML=Y.play),this.container.classList.remove("aplayer-loading"),this.timer.disable("loading")}},{key:"pause",value:function(){this.setUIPaused(),this.audio.pause()}},{key:"switchVolumeIcon",value:function(){this.volume()>=.95?this.template.volumeButton.innerHTML=Y.volumeUp:this.volume()>0?this.template.volumeButton.innerHTML=Y.volumeDown:this.template.volumeButton.innerHTML=Y.volumeOff}},{key:"volume",value:function(e,a){return e=parseFloat(e),isNaN(e)||(e=Math.max(e,0),e=Math.min(e,1),this.bar.set("volume",e,"height"),a||this.storage.set("volume",e),this.audio.volume=e,this.audio.muted&&(this.audio.muted=!1),this.switchVolumeIcon()),this.audio.muted?0:this.audio.volume}},{key:"on",value:function(e,a){this.events.on(e,a)}},{key:"toggle",value:function(){this.template.button.classList.contains("aplayer-play")?this.play():this.template.button.classList.contains("aplayer-pause")&&this.pause()}},{key:"switchAudio",value:function(e){this.list.switch(e)}},{key:"addAudio",value:function(e){this.list.add(e)}},{key:"removeAudio",value:function(e){this.list.remove(e)}},{key:"destroy",value:function(){ue.splice(ue.indexOf(this),1),this.pause(),this.container.innerHTML="",this.audio.src="",this.timer.destroy(),this.events.trigger("destroy")}},{key:"setMode",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"normal";this.mode=e,"mini"===e?this.container.classList.add("aplayer-narrow"):"normal"===e&&this.container.classList.remove("aplayer-narrow")}},{key:"notice",value:function(e){var a=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.8;this.template.notice.innerHTML=e,this.template.notice.style.opacity=r,this.noticeTime&&clearTimeout(this.noticeTime),this.events.trigger("noticeshow",{text:e}),t&&(this.noticeTime=setTimeout((function(){a.template.notice.style.opacity=0,a.events.trigger("noticehide")}),t))}},{key:"prevIndex",value:function(){if(!(this.list.audios.length>1))return 0;if("list"===this.options.order)return this.list.index-1<0?this.list.audios.length-1:this.list.index-1;if("random"===this.options.order){var e=this.randomOrder.indexOf(this.list.index);return 0===e?this.randomOrder[this.randomOrder.length-1]:this.randomOrder[e-1]}}},{key:"nextIndex",value:function(){if(!(this.list.audios.length>1))return 0;if("list"===this.options.order)return(this.list.index+1)%this.list.audios.length;if("random"===this.options.order){var e=this.randomOrder.indexOf(this.list.index);return e===this.randomOrder.length-1?this.randomOrder[0]:this.randomOrder[e+1]}}},{key:"skipBack",value:function(){this.list.switch(this.prevIndex())}},{key:"skipForward",value:function(){this.list.switch(this.nextIndex())}},{key:"duration",get:function(){return isNaN(this.audio.duration)?0:this.audio.duration}}])&&Ae(a.prototype,t),r&&Ae(a,r),e}();console.log("\n".concat(" %c APlayer v","1.10.1"," ").concat("337c026"," %c http://aplayer.js.org ","\n"),"color: #fadfa3; background: #030307; padding:5px 0;","background: #fadfa3; padding:5px 0;");a.default=de}]).default}));
//# sourceMappingURL=APlayer.min.js.map