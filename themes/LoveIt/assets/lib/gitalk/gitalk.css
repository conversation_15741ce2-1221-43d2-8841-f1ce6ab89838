@font-face {
  font-family: octicons-link;
  src: url(data:font/woff;charset=utf-8;base64,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) format('woff');
}

.markdown-body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  line-height: 1.5;
  color: #24292e;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.markdown-body .pl-c {
  color: #6a737d;
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
  color: #005cc5;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
  color: #6f42c1;
}

.markdown-body .pl-smi,
.markdown-body .pl-s .pl-s1 {
  color: #24292e;
}

.markdown-body .pl-ent {
  color: #22863a;
}

.markdown-body .pl-k {
  color: #d73a49;
}

.markdown-body .pl-s,
.markdown-body .pl-pds,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sre,
.markdown-body .pl-sr .pl-sra {
  color: #032f62;
}

.markdown-body .pl-v,
.markdown-body .pl-smw {
  color: #e36209;
}

.markdown-body .pl-bu {
  color: #b31d28;
}

.markdown-body .pl-ii {
  color: #fafbfc;
  background-color: #b31d28;
}

.markdown-body .pl-c2 {
  color: #fafbfc;
  background-color: #d73a49;
}

.markdown-body .pl-c2::before {
  content: "^M";
}

.markdown-body .pl-sr .pl-cce {
  font-weight: bold;
  color: #22863a;
}

.markdown-body .pl-ml {
  color: #735c0f;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
  font-weight: bold;
  color: #005cc5;
}

.markdown-body .pl-mi {
  font-style: italic;
  color: #24292e;
}

.markdown-body .pl-mb {
  font-weight: bold;
  color: #24292e;
}

.markdown-body .pl-md {
  color: #b31d28;
  background-color: #ffeef0;
}

.markdown-body .pl-mi1 {
  color: #22863a;
  background-color: #f0fff4;
}

.markdown-body .pl-mc {
  color: #e36209;
  background-color: #ffebda;
}

.markdown-body .pl-mi2 {
  color: #f6f8fa;
  background-color: #005cc5;
}

.markdown-body .pl-mdr {
  font-weight: bold;
  color: #6f42c1;
}

.markdown-body .pl-ba {
  color: #586069;
}

.markdown-body .pl-sg {
  color: #959da5;
}

.markdown-body .pl-corl {
  text-decoration: underline;
  color: #032f62;
}

.markdown-body .octicon {
  display: inline-block;
  vertical-align: text-top;
  fill: currentColor;
}

.markdown-body a {
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}

.markdown-body a:active,
.markdown-body a:hover {
  outline-width: 0;
}

.markdown-body strong {
  font-weight: inherit;
}

.markdown-body strong {
  font-weight: bolder;
}

.markdown-body h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.markdown-body img {
  border-style: none;
}

.markdown-body svg:not(:root) {
  overflow: hidden;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

.markdown-body hr {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  height: 0;
  overflow: visible;
}

.markdown-body input {
  font: inherit;
  margin: 0;
}

.markdown-body input {
  overflow: visible;
}

.markdown-body [type="checkbox"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0;
}

.markdown-body * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.markdown-body input {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.markdown-body a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body strong {
  font-weight: 600;
}

.markdown-body hr {
  height: 0;
  margin: 15px 0;
  overflow: hidden;
  background: transparent;
  border: 0;
  border-bottom: 1px solid #dfe2e5;
}

.markdown-body hr::before {
  display: table;
  content: "";
}

.markdown-body hr::after {
  display: table;
  clear: both;
  content: "";
}

.markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-body td,
.markdown-body th {
  padding: 0;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body h1 {
  font-size: 32px;
  font-weight: 600;
}

.markdown-body h2 {
  font-size: 24px;
  font-weight: 600;
}

.markdown-body h3 {
  font-size: 20px;
  font-weight: 600;
}

.markdown-body h4 {
  font-size: 16px;
  font-weight: 600;
}

.markdown-body h5 {
  font-size: 14px;
  font-weight: 600;
}

.markdown-body h6 {
  font-size: 12px;
  font-weight: 600;
}

.markdown-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.markdown-body blockquote {
  margin: 0;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body ol ol,
.markdown-body ul ol {
  list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.markdown-body dd {
  margin-left: 0;
}

.markdown-body code {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 12px;
}

.markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font: 12px "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
}

.markdown-body .octicon {
  vertical-align: text-bottom;
}

.markdown-body .pl-0 {
  padding-left: 0 !important;
}

.markdown-body .pl-1 {
  padding-left: 4px !important;
}

.markdown-body .pl-2 {
  padding-left: 8px !important;
}

.markdown-body .pl-3 {
  padding-left: 16px !important;
}

.markdown-body .pl-4 {
  padding-left: 24px !important;
}

.markdown-body .pl-5 {
  padding-left: 32px !important;
}

.markdown-body .pl-6 {
  padding-left: 40px !important;
}

.markdown-body::before {
  display: table;
  content: "";
}

.markdown-body::after {
  display: table;
  clear: both;
  content: "";
}

.markdown-body>*:first-child {
  margin-top: 0 !important;
}

.markdown-body>*:last-child {
  margin-bottom: 0 !important;
}

.markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.markdown-body .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.markdown-body .anchor:focus {
  outline: none;
}

.markdown-body p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body table,
.markdown-body pre {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-body blockquote>:first-child {
  margin-top: 0;
}

.markdown-body blockquote>:last-child {
  margin-bottom: 0;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: solid 1px #c6cbd1;
  border-bottom-color: #959da5;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 -1px 0 #959da5;
          box-shadow: inset 0 -1px 0 #959da5;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 .octicon-link,
.markdown-body h2 .octicon-link,
.markdown-body h3 .octicon-link,
.markdown-body h4 .octicon-link,
.markdown-body h5 .octicon-link,
.markdown-body h6 .octicon-link {
  color: #1b1f23;
  vertical-align: middle;
  visibility: hidden;
}

.markdown-body h1:hover .anchor,
.markdown-body h2:hover .anchor,
.markdown-body h3:hover .anchor,
.markdown-body h4:hover .anchor,
.markdown-body h5:hover .anchor,
.markdown-body h6:hover .anchor {
  text-decoration: none;
}

.markdown-body h1:hover .anchor .octicon-link,
.markdown-body h2:hover .anchor .octicon-link,
.markdown-body h3:hover .anchor .octicon-link,
.markdown-body h4:hover .anchor .octicon-link,
.markdown-body h5:hover .anchor .octicon-link,
.markdown-body h6:hover .anchor .octicon-link {
  visibility: visible;
}

.markdown-body h1 {
  padding-bottom: 0.3em;
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
}

.markdown-body h2 {
  padding-bottom: 0.3em;
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body h4 {
  font-size: 1em;
}

.markdown-body h5 {
  font-size: 0.875em;
}

.markdown-body h6 {
  font-size: 0.85em;
  color: #6a737d;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body li>p {
  margin-top: 16px;
}

.markdown-body li+li {
  margin-top: 0.25em;
}

.markdown-body dl {
  padding: 0;
}

.markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: 600;
}

.markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
}

.markdown-body table th {
  font-weight: 600;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.markdown-body img {
  max-width: 100%;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  background-color: #fff;
}

.markdown-body code {
  padding: 0;
  padding-top: 0.2em;
  padding-bottom: 0.2em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27,31,35,0.05);
  border-radius: 3px;
}

.markdown-body code::before,
.markdown-body code::after {
  letter-spacing: -0.2em;
  content: "\A0";
}

.markdown-body pre {
  word-wrap: normal;
}

.markdown-body pre>code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.markdown-body .highlight {
  margin-bottom: 16px;
}

.markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.markdown-body .highlight pre,
.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-body pre code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.markdown-body pre code::before,
.markdown-body pre code::after {
  content: normal;
}

.markdown-body .full-commit .btn-outline:not(:disabled):hover {
  color: #005cc5;
  border-color: #005cc5;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  line-height: 10px;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: solid 1px #d1d5da;
  border-bottom-color: #c6cbd1;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 -1px 0 #c6cbd1;
          box-shadow: inset 0 -1px 0 #c6cbd1;
}

.markdown-body :checked+.radio-label {
  position: relative;
  z-index: 1;
  border-color: #0366d6;
}

.markdown-body .task-list-item {
  list-style-type: none;
}

.markdown-body .task-list-item+.task-list-item {
  margin-top: 3px;
}

.markdown-body .task-list-item input {
  margin: 0 0.2em 0.25em -1.6em;
  vertical-align: middle;
}

.markdown-body hr {
  border-bottom-color: #eee;
}
/* variables */
/* functions & mixins */
/* variables - calculated */
/* styles */
.gt-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  font-size: 16px;
/* loader */
/* error */
/* initing */
/* no int */
/* link */
/* meta */
/* popup */
/* header */
/* comments */
/* comment */
}
.gt-container * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.gt-container a {
  color: #6190e8;
}
.gt-container a:hover {
  color: #81a6ed;
  border-color: #81a6ed;
}
.gt-container a.is--active {
  color: #333;
  cursor: default !important;
}
.gt-container a.is--active:hover {
  color: #333;
}
.gt-container .hide {
  display: none !important;
}
.gt-container .gt-svg {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: sub;
}
.gt-container .gt-svg svg {
  width: 100%;
  height: 100%;
  fill: #6190e8;
}
.gt-container .gt-ico {
  display: inline-block;
}
.gt-container .gt-ico-text {
  margin-left: 0.3125em;
}
.gt-container .gt-ico-github {
  width: 100%;
  height: 100%;
}
.gt-container .gt-ico-github .gt-svg {
  width: 100%;
  height: 100%;
}
.gt-container .gt-ico-github svg {
  fill: inherit;
}
.gt-container .gt-spinner {
  position: relative;
}
.gt-container .gt-spinner::before {
  content: '';
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  position: absolute;
  top: 3px;
  width: 0.75em;
  height: 0.75em;
  margin-top: -0.1875em;
  margin-left: -0.375em;
  border-radius: 50%;
  border: 1px solid #fff;
  border-top-color: #6190e8;
  -webkit-animation: gt-kf-rotate 0.6s linear infinite;
          animation: gt-kf-rotate 0.6s linear infinite;
}
.gt-container .gt-loader {
  position: relative;
  border: 1px solid #999;
  -webkit-animation: ease gt-kf-rotate 1.5s infinite;
          animation: ease gt-kf-rotate 1.5s infinite;
  display: inline-block;
  font-style: normal;
  width: 1.75em;
  height: 1.75em;
  line-height: 1.75em;
  border-radius: 50%;
}
.gt-container .gt-loader:before {
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: 50%;
  margin-top: -0.1875em;
  margin-left: -0.1875em;
  width: 0.375em;
  height: 0.375em;
  background-color: #999;
  border-radius: 50%;
}
.gt-container .gt-avatar {
  display: inline-block;
  width: 3.125em;
  height: 3.125em;
}
@media (max-width: 479px) {
  .gt-container .gt-avatar {
    width: 2em;
    height: 2em;
  }
}
.gt-container .gt-avatar img {
  width: 100%;
  height: auto;
  border-radius: 3px;
}
.gt-container .gt-avatar-github {
  width: 3em;
  height: 3em;
  cursor: pointer;
}
@media (max-width: 479px) {
  .gt-container .gt-avatar-github {
    width: 1.875em;
    height: 1.875em;
  }
}
.gt-container .gt-btn {
  padding: 0.75em 1.25em;
  display: inline-block;
  line-height: 1;
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #6190e8;
  border-radius: 5px;
  background-color: #6190e8;
  color: #fff;
  outline: none;
  font-size: 0.75em;
}
.gt-container .gt-btn-text {
  font-weight: 400;
}
.gt-container .gt-btn-loading {
  position: relative;
  margin-left: 0.5em;
  display: inline-block;
  width: 0.75em;
  height: 1em;
  vertical-align: top;
}
.gt-container .gt-btn.is--disable {
  cursor: not-allowed;
  opacity: 0.5;
}
.gt-container .gt-btn-login {
  margin-right: 0;
}
.gt-container .gt-btn-preview {
  background-color: #fff;
  color: #6190e8;
}
.gt-container .gt-btn-preview:hover {
  background-color: #f2f2f2;
  border-color: #81a6ed;
}
.gt-container .gt-btn-public:hover {
  background-color: #81a6ed;
  border-color: #81a6ed;
}
.gt-container .gt-error {
  text-align: center;
  margin: 0.625em;
  color: #ff3860;
}
.gt-container .gt-initing {
  padding: 1.25em 0;
  text-align: center;
}
.gt-container .gt-initing-text {
  margin: 0.625em auto;
  font-size: 92%;
}
.gt-container .gt-no-init {
  padding: 1.25em 0;
  text-align: center;
}
.gt-container .gt-link {
  border-bottom: 1px dotted #6190e8;
}
.gt-container .gt-link-counts,
.gt-container .gt-link-project {
  text-decoration: none;
}
.gt-container .gt-meta {
  margin: 1.25em 0;
  padding: 1em 0;
  position: relative;
  border-bottom: 1px solid #e9e9e9;
  font-size: 1em;
  position: relative;
  z-index: 10;
}
.gt-container .gt-meta:before,
.gt-container .gt-meta:after {
  content: " ";
  display: table;
}
.gt-container .gt-meta:after {
  clear: both;
}
.gt-container .gt-counts {
  margin: 0 0.625em 0 0;
}
.gt-container .gt-user {
  float: right;
  margin: 0;
  font-size: 92%;
}
.gt-container .gt-user-pic {
  width: 16px;
  height: 16px;
  vertical-align: top;
  margin-right: 0.5em;
}
.gt-container .gt-user-inner {
  display: inline-block;
  cursor: pointer;
}
.gt-container .gt-user .gt-ico {
  margin: 0 0 0 0.3125em;
}
.gt-container .gt-user .gt-ico svg {
  fill: inherit;
}
.gt-container .gt-user .is--poping .gt-ico svg {
  fill: #6190e8;
}
.gt-container .gt-version {
  color: #a1a1a1;
  margin-left: 0.375em;
}
.gt-container .gt-copyright {
  margin: 0 0.9375em 0.5em;
  border-top: 1px solid #e9e9e9;
  padding-top: 0.5em;
}
.gt-container .gt-popup {
  position: absolute;
  right: 0;
  top: 2.375em;
  background: #fff;
  display: inline-block;
  border: 1px solid #e9e9e9;
  padding: 0.625em 0;
  font-size: 0.875em;
  letter-spacing: 0.5px;
}
.gt-container .gt-popup .gt-action {
  cursor: pointer;
  display: block;
  margin: 0.5em 0;
  padding: 0 1.125em;
  position: relative;
  text-decoration: none;
}
.gt-container .gt-popup .gt-action.is--active:before {
  content: '';
  width: 0.25em;
  height: 0.25em;
  background: #6190e8;
  position: absolute;
  left: 0.5em;
  top: 0.4375em;
}
.gt-container .gt-header {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.gt-container .gt-header-comment {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-left: 1.25em;
}
@media (max-width: 479px) {
  .gt-container .gt-header-comment {
    margin-left: 0.875em;
  }
}
.gt-container .gt-header-textarea {
  padding: 0.75em;
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  min-height: 5.125em;
  max-height: 15em;
  border-radius: 5px;
  border: 1px solid rgba(0,0,0,0.1);
  font-size: 0.875em;
  word-wrap: break-word;
  resize: vertical;
  background-color: #f6f6f6;
  outline: none;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}
.gt-container .gt-header-textarea:hover {
  background-color: #fbfbfb;
}
.gt-container .gt-header-preview {
  padding: 0.75em;
  border-radius: 5px;
  border: 1px solid rgba(0,0,0,0.1);
  background-color: #f6f6f6;
}
.gt-container .gt-header-controls {
  position: relative;
  margin: 0.75em 0 0;
}
.gt-container .gt-header-controls:before,
.gt-container .gt-header-controls:after {
  content: " ";
  display: table;
}
.gt-container .gt-header-controls:after {
  clear: both;
}
@media (max-width: 479px) {
  .gt-container .gt-header-controls {
    margin: 0;
  }
}
.gt-container .gt-header-controls-tip {
  font-size: 0.875em;
  color: #6190e8;
  text-decoration: none;
  vertical-align: sub;
}
@media (max-width: 479px) {
  .gt-container .gt-header-controls-tip {
    display: none;
  }
}
.gt-container .gt-header-controls .gt-btn {
  float: right;
  margin-left: 1.25em;
}
@media (max-width: 479px) {
  .gt-container .gt-header-controls .gt-btn {
    float: none;
    width: 100%;
    margin: 0.75em 0 0;
  }
}
.gt-container:after {
  content: '';
  position: fixed;
  bottom: 100%;
  left: 0;
  right: 0;
  top: 0;
  opacity: 0;
}
.gt-container.gt-input-focused {
  position: relative;
}
.gt-container.gt-input-focused:after {
  content: '';
  position: fixed;
  bottom: 0%;
  left: 0;
  right: 0;
  top: 0;
  background: #000;
  opacity: 0.6;
  -webkit-transition: opacity 0.3s, bottom 0s;
  transition: opacity 0.3s, bottom 0s;
  z-index: 9999;
}
.gt-container.gt-input-focused .gt-header-comment {
  z-index: 10000;
}
.gt-container .gt-comments {
  padding-top: 1.25em;
}
.gt-container .gt-comments-null {
  text-align: center;
}
.gt-container .gt-comments-controls {
  margin: 1.25em 0;
  text-align: center;
}
.gt-container .gt-comment {
  position: relative;
  padding: 0.625em 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.gt-container .gt-comment-content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  margin-left: 1.25em;
  padding: 0.75em 1em;
  background-color: #f9f9f9;
  overflow: auto;
  -webkit-transition: all ease 0.25s;
  transition: all ease 0.25s;
}
.gt-container .gt-comment-content:hover {
  -webkit-box-shadow: 0 0.625em 3.75em 0 #f4f4f4;
          box-shadow: 0 0.625em 3.75em 0 #f4f4f4;
}
@media (max-width: 479px) {
  .gt-container .gt-comment-content {
    margin-left: 0.875em;
    padding: 0.625em 0.75em;
  }
}
.gt-container .gt-comment-header {
  margin-bottom: 0.5em;
  font-size: 0.875em;
  position: relative;
}
.gt-container .gt-comment-block-1 {
  float: right;
  height: 1.375em;
  width: 2em;
}
.gt-container .gt-comment-block-2 {
  float: right;
  height: 1.375em;
  width: 4em;
}
.gt-container .gt-comment-username {
  font-weight: 500;
  color: #6190e8;
  text-decoration: none;
}
.gt-container .gt-comment-username:hover {
  text-decoration: underline;
}
.gt-container .gt-comment-text {
  margin-left: 0.5em;
  color: #a1a1a1;
}
.gt-container .gt-comment-date {
  margin-left: 0.5em;
  color: #a1a1a1;
}
.gt-container .gt-comment-like,
.gt-container .gt-comment-edit,
.gt-container .gt-comment-reply {
  position: absolute;
  height: 1.375em;
}
.gt-container .gt-comment-like:hover,
.gt-container .gt-comment-edit:hover,
.gt-container .gt-comment-reply:hover {
  cursor: pointer;
}
.gt-container .gt-comment-like {
  top: 0;
  right: 2em;
}
.gt-container .gt-comment-edit,
.gt-container .gt-comment-reply {
  top: 0;
  right: 0;
}
.gt-container .gt-comment-body {
  color: #333 !important;
}
.gt-container .gt-comment-body .email-hidden-toggle a {
  display: inline-block;
  height: 12px;
  padding: 0 9px;
  font-size: 12px;
  font-weight: 600;
  line-height: 6px;
  color: #444d56;
  text-decoration: none;
  vertical-align: middle;
  background: #dfe2e5;
  border-radius: 1px;
}
.gt-container .gt-comment-body .email-hidden-toggle a:hover {
  background-color: #c6cbd1;
}
.gt-container .gt-comment-body .email-hidden-reply {
  display: none;
  white-space: pre-wrap;
}
.gt-container .gt-comment-body .email-hidden-reply .email-signature-reply {
  padding: 0 15px;
  margin: 15px 0;
  color: #586069;
  border-left: 4px solid #dfe2e5;
}
.gt-container .gt-comment-body .email-hidden-reply.expanded {
  display: block;
}
.gt-container .gt-comment-admin .gt-comment-content {
  background-color: #f6f9fe;
}
@-webkit-keyframes gt-kf-rotate {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes gt-kf-rotate {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

/*# sourceMappingURL=gitalk.css.map*/