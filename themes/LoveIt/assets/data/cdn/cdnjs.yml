prefix:
  libFiles: https://cdnjs.cloudflare.com/ajax/libs/
  # simple-icons@14.1.0 https://github.com/simple-icons/simple-icons
  simpleIcons: https://cdnjs.cloudflare.com/ajax/libs/simple-icons/14.1.0/
libFiles:
  # fontawesome-free@6.7.2 https://fontawesome.com/
  fontawesomeFreeCSS: font-awesome/6.7.2/css/all.min.css
  # animate.css@4.1.1 https://github.com/daneden/animate.css
  animateCSS: animate.css/4.1.1/animate.min.css
  # autocomplete.js@0.38.1 https://github.com/algolia/autocomplete.js
  autocompleteJS: autocomplete.js/0.38.1/autocomplete.min.js
  # lunr.js@2.3.9 https://lunrjs.com/
  lunrJS: lunr.js/2.3.9/lunr.min.js
  # algoliasearch@5.19.0 https://github.com/algolia/algoliasearch-client-javascript
  algoliasearchJS: algoliasearch/5.19.0/lite/builds/browser.umd.min.js
  # lazysizes@5.3.2 https://github.com/aFarkas/lazysizes
  lazysizesJS: lazysizes/5.3.2/lazysizes.min.js
  # object-fit-images@3.2.4 https://github.com/fregante/object-fit-images
  objectFitImagesJS: object-fit-images/3.2.4/ofi.min.js
  # lightgallery@2.5.0 https://github.com/sachinchoolur/lightgallery
  lightgalleryCSS: lightgallery/2.5.0/css/lightgallery-bundle.min.css
  lightgalleryJS: lightgallery/2.5.0/lightgallery.min.js
  lightgalleryThumbnailJS: lightgallery/2.5.0/plugins/thumbnail/lg-thumbnail.min.js
  lightgalleryZoomJS: lightgallery/2.5.0/plugins/zoom/lg-zoom.min.js
  # clipboard.js@2.0.11 https://github.com/zenorocha/clipboard.js
  clipboardJS: clipboard.js/2.0.11/clipboard.min.js
  # sharer.js@0.5.2 https://github.com/ellisonleao/sharer.js
  sharerJS: sharer.js/0.5.2/sharer.min.js
  # typeit@8.6.0 https://github.com/alexmacarthur/typeit
  typeitJS: typeit/8.6.0/index.umd.js
  # katex@0.16.9 https://katex.org/
  katexCSS: KaTeX/0.16.9/katex.min.css
  katexJS: KaTeX/0.16.9/katex.min.js
  katexAutoRenderJS: KaTeX/0.16.9/contrib/auto-render.min.js
  katexCopyTexJS: KaTeX/0.16.9/contrib/copy-tex.min.js
  katexMhchemJS: KaTeX/0.16.9/contrib/mhchem.min.js
  # mermaid@11.4.0 https://github.com/mermaid-js/mermaid
  mermaidJS: mermaid/11.4.0/mermaid.min.js
  # echarts@5.6.0 https://echarts.apache.org/
  echartsJS: echarts/5.6.0/echarts.min.js
  # mapbox-gl@2.9.1 https://docs.mapbox.com/mapbox-gl-js
  mapboxGLCSS: mapbox-gl/2.9.1/mapbox-gl.min.css
  mapboxGLJS: mapbox-gl/2.9.1/mapbox-gl.min.js
  # aplayer@1.10.1 https://github.com/MoePlayer/APlayer
  aplayerCSS: aplayer/1.10.1/APlayer.min.css
  #aplayerJS: aplayer/1.10.1/APlayer.min.js
  # gitalk@1.7.2 https://github.com/gitalk/gitalk
  gitalkCSS: gitalk/1.7.2/gitalk.min.css
  gitalkJS: gitalk/1.7.2/gitalk.min.js
  # valine@1.5.3 https://valine.js.org/
  valineJS: valine/1.5.3/Valine.min.js
  # cookieconsent@3.1.1 https://github.com/osano/cookieconsent
  cookieconsentCSS: cookieconsent/3.1.1/cookieconsent.min.css
  cookieconsentJS: cookieconsent/3.1.1/cookieconsent.min.js
  # waline@2.6.1 https://waline.js.org/
  walineCSS: waline/2.6.1/waline.css
  walineJS: waline/2.6.1/waline.js
