.admonition {
  position: relative;
  margin: 1rem 0;
  padding: 0 .75rem;
  border-left: .25rem solid;
  overflow: auto;

  .admonition-title {
    font-weight: bold;
    margin: 0 -0.75rem;
    padding: .25rem 1.8rem;
  }

  .admonition-content {
    padding: .5rem 0;
  }

  i.icon {
    font-size: 0.85rem;
    position: absolute;
    top: .6rem;
    left: .4rem;
  }

  i.details-icon {
    position: absolute;
    top: .6rem;
    right: .3rem;
  }

  @each $type, $color, $background-color in $admonition-color-list {
    @if $type == "note" {
      background-color: $background-color;
      border-left-color: $color;

      .admonition-title {
        border-bottom-color: $background-color;
        background-color: opacify($background-color, 0.15);
      }

      &.open .admonition-title {
        background-color: $background-color;
      }

      i.icon {
        color: $color;
      }
    } @else {
      &.#{$type} {
        background-color: $background-color;
        border-left-color: $color;

        .admonition-title {
          border-bottom-color: $background-color;
          background-color: opacify($background-color, 0.15);
        }

        &.open .admonition-title {
          background-color: $background-color;
        }

        i.icon {
          color: $color;
        }
      }
    }
  }

  &:last-child {
    margin-bottom: .75rem;
  }
}
