.toc {
  .toc-title {
    font-size: var(--toc-title-font-size);
    font-weight: bold;
    text-transform: uppercase;
  }

  .toc-content {
    font-size: var(--toc-content-font-size);

    ul {
      text-indent: -0.85rem;
      padding-left: .8rem;
      list-style: none;

      a:first-child::before {
        content: "|";
        font-weight: bolder;
        margin-right: .5rem;
        color: $single-link-color;

        [theme=dark] & {
          color: $single-link-color-dark;
        }
      }

      ul {
        padding-left: 1.5rem;
      }
    }
  }

  ruby {
    background: $code-background-color;

    rt {
      color: $global-font-secondary-color;
    }

    [theme=dark] & {
      background: $code-background-color-dark;

      rt {
        color: $global-font-secondary-color-dark;
      }
    }
  }
}

#toc-auto {
  display: block;
  position: absolute;
  width: 10000px;
  max-width: 0;
  padding: 0 .8rem;
  border-left: 4px solid $global-border-color;
  @include overflow-wrap(break-word);
  box-sizing: border-box;
  top: 10rem;
  left: 0;
  visibility: hidden;

  [data-header-desktop=normal] & {
    top: 5rem;
  }

  @include blur;

  [theme=dark] & {
    border-left-color: $global-border-color-dark;
  }

  .toc-title {
    margin: .8rem 0;
  }

  .toc-content {
    &.always-active ul {
      display: block;
    }

    > nav > ul {
      margin: .625rem 0;
    }

    ul {
      ul {
        display: none;
      }

      .has-active > ul {
        display: block;
      }
    }

    a.active {
      font-weight: bold;
      color: $single-link-color;

      [theme=dark] & {
        color: $single-link-color-dark;
      }

      &::before {
        color: $single-link-hover-color;

        [theme=dark] & {
          color: $single-link-hover-color-dark;
        }
      }
    }
  }
}

#toc-static {
  display: none;
  margin: .8rem 0;

  &[data-kept=true] {
    display: block;
  }

  .toc-title {
    display: flex;
    justify-content: space-between;
    line-height: 2em;
    padding: 0 .75rem;
    background: darken($code-background-color, 6%);

    [theme=dark] & {
      background: darken($code-background-color-dark, 6%);
    }
  }

  .toc-content {
    background-color: $code-background-color;

    > nav > ul {
      margin: 0;
      padding: .4rem 1rem .4rem 1.8rem;
    }

    [theme=dark] & {
      background-color: $code-background-color-dark;
    }
  }

  &.open {
    .toc-title {
      background: darken($code-background-color, 3%);

      [theme=dark] & {
        background: darken($code-background-color-dark, 3%);
      }
    }
  }
}
