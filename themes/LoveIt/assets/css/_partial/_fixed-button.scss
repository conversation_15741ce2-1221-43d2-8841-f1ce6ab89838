#fixed-buttons {
  display: none;
}

#fixed-buttons-hidden {
  display: none;
}

.fixed-button {
  display: block;
  z-index: 100;
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.3rem;
  padding: .6rem .6rem;
  color: $global-font-secondary-color;
  background: $header-background-color;
  @include border-radius(2rem);
  @include transition(color 0.4s ease);

  @include blur;

  &:hover, &:active {
    color: $global-font-color;
    cursor: pointer;
  }

  &:active, &:focus, &:hover {
    outline: none;
  }

  [theme=dark] & {
    color: $global-font-secondary-color-dark;
    background: $header-background-color-dark;

    &:hover, &:active {
      color: $global-font-color-dark;
    }
  }

  &:nth-last-of-type(1) {
    bottom: 1.5rem;
  }

  &:nth-last-of-type(2) {
    bottom: 4.5rem;
  }

  &:nth-last-of-type(3) {
    bottom: 7.5rem;
  }
}
